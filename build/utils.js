const fs = require('fs');
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

// pages
const PAGES_PATH = path.resolve(__dirname, '../src/pages');
let dir = fs.readdirSync(PAGES_PATH, {
  withFileTypes: false,
});

// 检查合法的文件
dir = dir.filter((dirent) => {
  if (fs.statSync(path.resolve(PAGES_PATH, dirent)).isDirectory()) {
    const files = fs.readdirSync(path.resolve(PAGES_PATH, dirent));
    return files.includes('index.ts');
  }

  return false;
});

// 创建多入口entry
exports.createEntries = () => {
  const entry = {};
  dir.forEach((dirent) => {
    entry[dirent] = path.resolve(PAGES_PATH, `${dirent}/index.ts`);
  });

  return entry;
};

// 创建多输出HtmlWebpackPlugin
exports.createHtmlWebpackPlugin = () => {
  const htmlWebpackPlugins = [];
  dir.forEach((dirent) => {
    const options = {
      title: 'WeMust',
      filename: `${dirent}.html`,
      template: path.resolve(__dirname, '../index.html'),
      chunks: ['manifest', 'commons', 'vendors', 'vue', 'core-js', 'lodash', 'date-fns', 'vant', dirent],
      options: {
        staticResourcesBaseUrl: process.env.STATIC_RESOURCES_BASE_URL,
      },
    };
    htmlWebpackPlugins.push(new HtmlWebpackPlugin(options));
  });

  return htmlWebpackPlugins;
};
