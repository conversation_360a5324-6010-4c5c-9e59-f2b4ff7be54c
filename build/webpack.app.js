const path = require('path');

const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

module.exports = () => {
  return {
    target: ['web', 'es5'],
    output: {
      path: resolve('../dist'), // 出口文件路径
      filename: 'app.js',
      library: {
        name: 'app',
        type: 'umd',
      },
    },
    entry: resolve('../src/app.ts'),
    module: {
      rules: [
        {
          test: /\.(ts|js)x?$/,
          exclude: /node_modules/,
          loader: 'babel-loader'
        },
      ],
    },
  };
}