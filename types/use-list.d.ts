
declare module '@/uses/use-list' {
  import type { Ref } from 'vue';

  interface Page {
    size: number,
    index: number,
  }

  interface IListResult<T> {
    data: Ref<Array<T>>,
    loading: Ref<boolean>,
    loadMoreIng: Ref<boolean>,
    refreshing: Ref<boolean>,
    finished: Ref<boolean>,
    error: Ref<boolean>,
    page: Ref<Page>,
    offset: Ref<number>,
    total: Ref<number>,
    load: () => Promise<CommonApiListData<T>>,
    loadMore: () => void,
    refresh: () => Promise<CommonApiListData<T>>,
    removeItem: (index: number) => void,
    reset: () => void,
  }

  export function useList<T>(options: object): IListResult<T>;
  export default Function;
}
