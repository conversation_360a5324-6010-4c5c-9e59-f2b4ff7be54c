{"name": "wm-service-app-js", "version": "0.1.0", "private": true, "description": "首頁APP", "author": "<EMAIL>", "license": "ISC", "scripts": {"build": "yarn run build:client && yarn run build:app", "build:client": "cross-env NODE_ENV=production webpack --config ./build/webpack.production.js --mode production --progress", "build:app": "webpack --config ./build/webpack.app.js --mode production", "preview": "webpack-cli serve --config ./build/webpack.production.js --mode production --progress", "dev": "cross-env NODE_ENV=development webpack-cli serve --config ./build/webpack.development.js --progress --hot", "analyz": "webpack --config ./build/webpack.production.js --mode production --progress --env report", "lint": "eslint --ext .js --ext .ts --ext .vue ./", "lint:css": "stylelint ./src/**/*.{css,less,vue}"}, "dependencies": {"@types/uuid": "^9.0.8", "axios": "^1.7.7", "core-js": "^3.33.3", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "eruda": "^3.0.1", "js-md5": "^0.8.3", "json-bigint": "^1.0.0", "lodash": "^4.17.21", "mitt": "^3.0.1", "nanoid": "^3.3.7", "pinia": "^2.2.6", "qs": "^6.12.3", "sanitize-html": "^2.11.0", "urijs": "^1.19.11", "uuid": "^9.0.1", "vant": "^4.8.0", "vue": "^3.5.13", "vue-i18n": "^10.0.5"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.3", "@babel/eslint-parser": "^7.23.3", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.23.3", "@babel/plugin-transform-object-rest-spread": "^7.23.4", "@babel/plugin-transform-runtime": "^7.23.4", "@babel/preset-env": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/eslint-plugin": "^5.56.0", "@typescript-eslint/parser": "^5.56.0", "@vue/compiler-sfc": "^3.3.8", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "dotenv-webpack": "^8.0.1", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "^2.29.0", "eslint-plugin-vue": "^9.18.1", "eslint-webpack-plugin": "^4.0.1", "html-webpack-plugin": "^5.5.3", "less": "^4.2.0", "less-loader": "^11.1.2", "mini-css-extract-plugin": "^2.7.6", "node-polyfill-webpack-plugin": "^2.0.1", "postcss": "~8.4.31", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "postcss-loader": "^7.3.3", "postcss-preset-env": "^9.3.0", "stylelint": "^15.11.0", "stylelint-config-rational-order-fix": "^0.1.9", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-less": "^2.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-less": "^2.0.0", "stylelint-order": "^6.0.3", "stylelint-webpack-plugin": "^4.1.1", "tslib": "^2.6.1", "typescript": "^4.9.4", "unplugin-vue-components": "^0.25.2", "vue-loader": "^17.3.1", "vue-style-loader": "^4.1.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.10.0"}, "resolutions": {"stylelint-config-standard": "34.0.0", "stylelint-config-recommended": "13.0.0"}, "engines": {"node": ">=16.18.0"}}