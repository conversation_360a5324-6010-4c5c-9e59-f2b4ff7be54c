# 首頁 App

## 安裝

### 編譯依賴

這個項目使用 [node](https://nodejs.org/) 和 [yarn](https://yarnpkg.com/)。請確保你本地安裝了它們。

- node 需要 [16.18.x](https://nodejs.org/en/download/) 或以上 TLS 版本（v17.x 以上未測試，不建議使用）
- yarn 需要 [1.22.x](https://yarnpkg.com/getting-started/install) 或以上版本

### 服務器依賴

- 原生 APP

  應用必須運行在《WeMust 教職員 APP》和《WeMust 學生 APP》原生 APP 中，其他運行環境暫不支持。含以下具體 APP：

  - [wm-staff-android](https://wm-git.must.edu.mo/wemust/wm-staff-android) WeMust 教職員原生安卓 APP
  - [wm-staff-ios](https://wm-git.must.edu.mo/wemust/wm-staff-ios) WeMust 教職員原生 iOS APP
  - [wm-student-android](https://wm-git.must.edu.mo/wemust/wm-student-android) WeMust 學生原生安卓 APP
  - [wm-student-ios](https://wm-git.must.edu.mo/wemust/wm-student-ios) WeMust 學生原生 iOS APP

- 外部依賴服務項

  服務運行時會訪問以下相關的外部依服資源，請務必先拿到相關信息，具體如下表所示：

| 依賴服務 | 項目名稱                                           | 備註         |
| -------- | -------------------------------------------------- | ------------ |
| 項目接口 | [dc-api-new](https://mustdev.doocom.cn/dc-api-new) | 業務數據交互 |

## 運行示例（測試用）

1. 克隆代碼，執行命令 `git clone git@172.16.124.48:wemust/wm-app-service-app-js.git`
2. 進入工程文件夾，執行命令 `cd wm-app-service-app-js`
3. 安裝項目依賴，執行命令 `yarn install --production=false`
4. 配置 [.env](#.env配置文件說明) 配置項
5. 啟動示例，執行命令 `yarn run serve`

## 構建（生產用）

1. 克隆代碼，執行命令 `git clone git@172.16.124.48:wemust/wm-app-service-app-js.git`
2. 進入工程文件夾，執行命令 `cd wm-app-service-app-js`
3. 安裝項目 Webpack 依賴，執行命令 `yarn install --production=true`
4. 配置 [.env](#.env配置文件說明) 配置項
5. Webpack 構建，執行命令 `yarn run build`

## 部署

### 複製環境生產使用文件

生產環境下，只需要使用部分文件夾和文件，具體如下：

| 路徑  | 備註 |
| ----- | ---- |
| dist/ |      |

### 在生產環境啟動項目

1. 需將生產環境構建的文件（dist 文件夾下），上傳至熱更新資源服務器
2. 熱更新資源服務器執行版本升級，可參考[《原生應用 HTML5 服務管理》](https://wm-git.must.edu.mo/wemust/wm-html5app-nodejs)
3. 在《數據中心》的《應用管理》中，添加新的應用版本

如果對以上的操作步驟存在疑問，可找開發或運維了解。

## .env 配置文件說明

項目運行前需要配置 .env 文件，.env 文件不存在項目的版本管理系統(git)當中，需要單獨創建，.env 配置文件需存放在項目根目錄中。

项目提供配置對照文件 .env.sample，可複制該文件創建 .env 文件。

請注意，提供 .env 配置文件的目的，是為了不能修改直接系統環境變量的情況下，做為補充的配置手段。如果你已經在環境變量配置對應的項，則可以不用創建 .env 文件。

### 項目 .env 文件所需的配置選項

| 鍵值         | 備註                | 必填 | 默認值           | 示例                                 | 說明 |
| ------------ | ------------------- | ---- | ---------------- | ------------------------------------ | ---- |
| API_BASE_URL | dc-api-new 接口 URL | ✓    |                  | https://mustdev.doocom.cn/dc-api-new |      |
| SERVICE_CODE | 服務編碼            | ✓    | S-WM-APP-SERVICE |                                      |      |

## 主要使用技術框架

- [webpack](https://webpack.js.org/)
- [Babel](https://babeljs.io/)
- [Vue.js](https://vuejs.org/)
- [vue-i18n](https://kazupon.github.io/vue-i18n/)
