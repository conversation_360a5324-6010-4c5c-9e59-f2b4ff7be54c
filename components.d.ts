/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ApplicationItem: typeof import('./src/components/biz/application-item.vue')['default']
    BottomAction: typeof import('./src/components/common/bottom-action.vue')['default']
    EmptyData: typeof import('./src/components/biz/empty-data.vue')['default']
    IconWithAiPrefix: typeof import('./src/components/biz/icon-with-ai-prefix.vue')['default']
    PageLoading: typeof import('./src/components/common/page-loading.vue')['default']
    PopupConfirmCancel: typeof import('./src/components/common/popup-confirm-cancel.vue')['default']
    PopupPicker: typeof import('./src/components/common/popup-picker.vue')['default']
    TitleBar: typeof import('./src/components/common/title-bar.vue')['default']
    ToastError: typeof import('./src/components/common/toast-error.vue')['default']
    ToastLoading: typeof import('./src/components/common/toast-loading.vue')['default']
    ToastSuccess: typeof import('./src/components/common/toast-success.vue')['default']
    TransitionFade: typeof import('./src/components/common/transition-fade.vue')['default']
    WmLoading: typeof import('./src/components/common/wm-loading.vue')['default']
    WmLoadingIcon: typeof import('./src/components/common/wm-loading-icon.vue')['default']
  }
}
