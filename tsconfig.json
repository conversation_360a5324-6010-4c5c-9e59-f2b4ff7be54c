{"compilerOptions": {"target": "es6", "module": "esnext", "strict": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "paths": {"tslib": ["path/to/node_modules/tslib/tslib.d.ts"], "@/*": ["src/*"]}, "lib": ["ES2015", "esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.vue", "types/*.d.ts"], "exclude": ["node_modules"]}