import _ from 'lodash';

interface PaginationParamsOption {
  page: {
    index: number,
    size: number,
  },
}

interface PaginationResult {
  offsetStart: number,
  maxPageItems: number,
}

export function paginationParams(params: PaginationParamsOption): PaginationResult {
  const o = _.cloneDeep(params);
  const offsetStart = _.get(o, 'page.index', 1);
  const maxPageItems = _.get(o, 'page.size', 20);
  delete o.page;
  Object.assign(o, {
    offsetStart,
    maxPageItems,
  });

  return o;
}

/**
 * 過濾掉null、undefined、空字串、[]的params
 * @param {Object} params 請求的params
 * @returns {Object} 返回過濾掉空值的params
 */
export function filterEmptyParams(params: Record<string, unknown>) {
  const o = _.cloneDeep(params);
  return _.pickBy(o, (value) => {
    const isNil = [null, undefined, ''].includes(value);
    const isEmpty = Array.isArray(value) && value.length === 0;
    return !isNil && !isEmpty;
  });
}
