/* eslint-disable @typescript-eslint/ban-ts-comment */
import axios, { AxiosInstance } from 'axios';
import qs from 'qs';
import jsonBigInt from 'json-bigint';

import nativeAdapter from './native-adapter';
import interceptorAuthorization from './interceptors/authorization';
import interceptorNullFilter from './interceptors/null-filter';
import interceptorAjax from './interceptors/ajax';
import interceptorSignature from './interceptors/signature';
import interceptorNonce from './interceptors/nonce';
import interceptorLocale from './interceptors/locale';

function readBlobError(reader) {
  return new Promise(((resolve) => {
    // eslint-disable-next-line
    reader.onload = () => {
      const data = JSON.parse(reader.result);
      const error = new Error(data.errorMsg);
      Object.assign(error, {
        code: data.errorCode,
        response: { data },
      });
      resolve(error);
    };
  }));
}

export default ({
  baseURL, salt, timeout, responseType, headers,
}): AxiosInstance => {
  const xhr = axios.create({
    // @ts-ignore
    adapter: nativeAdapter,
    baseURL,
    timeout: (Number.isFinite(timeout) ? timeout : 20 * 1000),
    responseType,
    headers: {
      ...headers,
    },
    paramsSerializer: {
      serialize(params) {
        return qs.stringify(params, { indices: false });
      },
    },
    transformResponse: [
      (data) => {
        try {
          return jsonBigInt({ storeAsString: true }).parse(data);
        } catch (error) {
          return data;
        }
      },
    ],
  });

  xhr.interceptors.request.use(
    interceptorAuthorization,
  );

  xhr.interceptors.request.use(
    interceptorNullFilter,
  );

  xhr.interceptors.request.use(
    interceptorAjax,
  );

  xhr.interceptors.request.use(
    interceptorSignature({ salt }),
  );

  xhr.interceptors.request.use(
    interceptorNonce,
  );

  xhr.interceptors.request.use(
    interceptorLocale,
  );

  // TODO: 仅用于调试
  // xhr.interceptors.request.use((config) => {
  //   console.log(config.baseURL, config.url, 'config.data:', config.data, 'config.params:', config.params);
  //   return config;
  // });

  xhr.interceptors.response.use(
    async (response) => {
      // TODO: 仅用于调试
      // console.log(response.config.url, 'response.data:', response.data);
      const { data } = response;

      if (data instanceof Blob) {
        // Blob报错情况处理
        if (!data.type.includes('application/json')) {
          return response;
        }

        const reader = new FileReader();
        reader.readAsText(data);
        const error = await readBlobError(reader);
        throw error;
      }

      return data;
    },
  );

  return xhr;
};
