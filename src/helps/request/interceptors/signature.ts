import _ from 'lodash';
import md5 from 'js-md5';


const isFormData = (thing) => thing instanceof FormData;

const pairsQuery = (query) => {
  const ps = _.toPairs(query).map(([k, v]) => {
    if (_.isArray(v) && !_.isEmpty(v)) {
      return [k, v[0]];
    }

    return [k, v];
  });

  return ps;
};

// 处理Body为正常的JSON键值对
const pairsBodyGeneral = (body) => {
  if (isFormData(body)) {
    return [];
  }

  if (_.isArray(body)) {
    return [];
  }

  const ps = _.toPairs(body).map(([k, v]) => {
    if (_.isObject(v)) {
      return [k, JSON.stringify(v)];
    }

    return [k, v];
  });

  return ps;
};

// 处理Body为JSON数组格式
const pairsBodyArray = (body) => {
  if (isFormData(body)) {
    return [];
  }

  if (!_.isArray(body)) {
    return [];
  }

  let pairs = [];
  _.forEach(body, (it) => {
    const c = _.map(_.keys(it).sort(), (k) => {
      const v = it[k];
      if (_.isObject(v)) {
        return [k, JSON.stringify(v)];
      }

      return [k, v];
    });

    pairs = pairs.concat(c);
  });

  return pairs;
};

const signatureString = (pairs, sort = true) => {
  const process = _.flow(
    (value) =>
      // eslint-disable-next-line no-unused-vars, implicit-arrow-linebreak, @typescript-eslint/no-unused-vars
      _.filter(value, ([k, v]) => !_.isNil(v)),

    (value) => {
      if (sort) {
        return _.sortBy(value, ([k]) => k);
      }

      return value;
    },

    (value) => value.map(([k, v]) => `${k}=${v}`).join(''),
  );

  return process(pairs);
};

const signature = (salt, query, body) => {
  const encrypted = (saltVal, raw) => md5(`${raw}${saltVal}`);

  const pq = pairsQuery(query);
  const pbg = pairsBodyGeneral(body);
  const pba = pairsBodyArray(body);
  const s1 = signatureString(pq.concat(pbg));
  const s2 = signatureString(pba, false);
  const es = encrypted(salt, `${s1}${s2}`);
  return es;
};

export default ({ salt }) => (request) => {
  const s = signature(
    salt,
    request.params,
    request.data,
  );

  Object.assign(request.params, {
    signature: s,
  });

  return request;
};
