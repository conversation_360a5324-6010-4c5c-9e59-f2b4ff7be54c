import { OperatingCode } from '@/consts/operating-code';
import namespaceT from '../namespace-t';

export function getOperatingCodeI18nText(key: OperatingCode): string {
  const t = namespaceT('consts.operatingCode');

  const mapper = new Map([
    [OperatingCode.CLOCK_IN_ACCESS, t(OperatingCode.CLOCK_IN_ACCESS)],
    [OperatingCode.CLOCK_IN_DORMITORY, t(OperatingCode.CLOCK_IN_DORMITORY)],
    [OperatingCode.ENTRANCE_GUARD_ACCESS, t(OperatingCode.ENTRANCE_GUARD_ACCESS)],
    [OperatingCode.RECHARGE_ACCESS, t(OperatingCode.RECHARGE_ACCESS)],
    [OperatingCode.SCAN_ACCESS, t(OperatingCode.SCAN_ACCESS)],
    [OperatingCode.WALLET_ACCESS, t(OperatingCode.WALLET_ACCESS)],
    [OperatingCode.DEBUG, t(OperatingCode.DEBUG)],
  ]);

  return mapper.get(key) || '';
}
