import { AirQualityLevel } from '@/consts/air-quality-level';
import namespaceT from '../namespace-t';

export function getAirQualityLevelI18nText(key: AirQualityLevel): string {
  const t = namespaceT('consts.airQualityLevel');

  const mapper = new Map([
    [AirQualityLevel.EXCELLENT, t(AirQualityLevel.EXCELLENT)],
    [AirQualityLevel.GOOD, t(AirQualityLevel.GOOD)],
    [AirQualityLevel.MILDLY_POLLUTED, t(AirQualityLevel.MILDLY_POLLUTED)],
    [AirQualityLevel.MODERATELY_POLLUTED, t(AirQualityLevel.MODERATELY_POLLUTED)],
    [AirQualityLevel.HEAVILY_POLLUTED, t(AirQualityLevel.HEAVILY_POLLUTED)],
    [AirQualityLevel.SEVERELY_POLLUTED, t(AirQualityLevel.SEVERELY_POLLUTED)],
  ]);

  return mapper.get(key) || '';
}
