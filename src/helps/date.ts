import { parse, isValid, format } from 'date-fns';
import formatInTimeZone from 'date-fns-tz/formatInTimeZone';

import { API_SERVER_TIME_ZONE, ApiDate } from '@/config/api';
import namespaceT from './namespace-t';


/** @function
 * 转换为日期
 * @param {(string|number)} dateString - 要解析的字符串或数字
 * @param {string} [formatString="yyyy-MM-dd'T'HH:mm:ssxxx"] - 转换的格式
 * @returns {(Date|undefined)} 日期
 */
export function dateConvert(dateString, formatString: string = ApiDate.DATE_TIME) {
  if (dateString instanceof Date) {
    return dateString;
  }

  if (typeof dateString === 'string') {
    const date = parse(dateString, formatString, new Date());

    if (isValid(date)) {
      return date;
    }
  } else if (typeof dateString === 'number') {
    const date = new Date(dateString);
    if (isValid(date)) {
      return date;
    }
  }

  return undefined;
}

/** @function
 * 日期格式化（带服务器时区，固定在+8000时区）
 * @param {(Date|string|number)} dateString - 要格式化的字符串或数字日期
 * @param {string} destFormatString - 输出时的格式
 * @param {string} [parseFormatString="yyyy-MM-dd'T'HH:mm:ssxxx"] - 转换时的格式
 * @param {string} [serverTimeZone="Asia/Shanghai"] - 转换时的服务器时区
 * @returns {string} 日期格式化后的字符串
 */
export function dateFormatSTZ(
  dateString,
  destFormatString,
  parseFormatString: string = ApiDate.DATE_TIME,
  serverTimeZone = API_SERVER_TIME_ZONE,
) {
  const date = dateConvert(dateString, parseFormatString);
  if (isValid(date)) {
    return formatInTimeZone(date, serverTimeZone, destFormatString);
  }

  return '';
}

/** @function
 * 日期格式化（本地时区）
 * @param {(Date|string|number)} dateString - 要格式化的字符串或数字日期
 * @param {string} destFormatString - 输出时的格式
 * @param {string?} [parseFormatString="yyyy-MM-dd"] - 转换时的格式
 * @returns {string} 日期格式化后的字符串
 */
export function dateFormat(
  dateString,
  destFormatString,
  parseFormatString: string = ApiDate.DATE,
) {
  const date = dateConvert(dateString, parseFormatString);
  if (isValid(date)) {
    return format(date, destFormatString);
  }

  return '';
}

export function formatFullTime(time: Date) {
  if (!isValid(time)) {
    return undefined;
  }

  return format(time, ApiDate.FULL_TIME);
}


// 日期字符串补秒和时间
export function padRightMinuteTimeZone(dateString) {
  return `${dateString}:00+08:00`;
}

// 时间字符串补秒
export function padRightMinute(timeString: string) {
  return `${timeString}:00`;
}

export function getTomorrow() {
  const tomorrow = new Date();
  tomorrow.setTime(tomorrow.getTime() + 24 * 60 * 60 * 1000);
  return tomorrow;
}

/** 遠程時間轉換至本地時間 */
export function remoteTimeToLocalTime(time: string) {
  const t = namespaceT('dateFormat');
  return dateFormat(time, t('time'), ApiDate.FULL_TIME);
}

export function remoteFullDateToLocalDate(time: string) {
  const t = namespaceT('dateFormat');
  return dateFormat(time, t('dateTime'), ApiDate.FULL_DATE_TIME_WITH_TIME_ZONE);
}
