import type { Composer } from 'vue-i18n';
import { i18n } from '@/i18n';


/** @function
 * 当前系统语言
 * @returns {string} 系统语言字符串
 */
export function currentLocale() {
  return (i18n.global as unknown as Composer).locale.value;
}

/** @function
 * 根据当前系统语言获取对应值
 * @param {(Function|string)} chnFn - 繁体中文值
 * @param {(Function|string)} engFn - 英文值
 * @returns {*} 系统语言对应值
 */
export function valueByLocale(chnFn, engFn) {
  if (currentLocale() === 'zh-Hant') {
    return typeof chnFn === 'function' ? chnFn() : chnFn;
  }

  return typeof engFn === 'function' ? engFn() : engFn;
}

/** @function
 * 根据当前系统语言获取对应文本
 * @param {string} chnText - 繁体中文文本
 * @param {string} engText - 英文文本
 * @param {boolean} [fallback=false] - 当前语言为空白时fallback文本
 * @returns {string} 系统语言对应文本
 */
export function textByLocale(chnText, engText, fallback = false) {
  if (fallback) {
    return valueByLocale(() => (chnText || engText), () => (engText || chnText));
  }

  return valueByLocale(chnText, engText);
}


export function currentLocalIsEn() {
  return currentLocale() === 'en';
}
