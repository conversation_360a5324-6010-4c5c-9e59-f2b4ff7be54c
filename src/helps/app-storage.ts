import { AppStorageKey } from '@/config/app-storage';
import { isNothing } from '@/utils/is-nothing';
import { generateUniqueStorageKey } from './generate-user-storage-key';

export function getAppStorageItem(key: AppStorageKey) {
  const transformedKey = generateUniqueStorageKey(key);
  const value = native.storage.getItem(transformedKey);
  if (isNothing(value)) {
    return undefined;
  }

  return JSON.parse(value);
}

export function removeAppStorageItem(key: AppStorageKey) {
  const transformedKey = generateUniqueStorageKey(key);
  native.storage.removeItem(transformedKey);
}


export function setAppStorageItem(key: AppStorageKey, value: object | string | unknown) {
  if (getAppStorageItem(key)) {
    removeAppStorageItem(key);
  }

  const data = JSON.stringify(value);
  const transformedKey = generateUniqueStorageKey(key);
  native.storage.setItem(transformedKey, data);
}
