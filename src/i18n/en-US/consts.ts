import { AirQualityLevel } from '@/consts/air-quality-level';
import { OperatingCode } from '@/consts/operating-code';

export default {
  operatingCode: {
    [OperatingCode.CLOCK_IN_ACCESS]: 'Clock in / out',
    [OperatingCode.CLOCK_IN_DORMITORY]: 'Dormitory Roll Call',
    [OperatingCode.WALLET_ACCESS]: 'Payment Code',
    [OperatingCode.RECHARGE_ACCESS]: 'Top up',
    [OperatingCode.ENTRANCE_GUARD_ACCESS]: 'Doors',
    [OperatingCode.SCAN_ACCESS]: 'Scan',
    [OperatingCode.DEBUG]: 'DEBUG',
  },

  airQualityLevel: {
    [AirQualityLevel.EXCELLENT]: 'Excellent',
    [AirQualityLevel.GOOD]: 'Good',
    [AirQualityLevel.MILDLY_POLLUTED]: 'Bad',
    [AirQualityLevel.MODERATELY_POLLUTED]: 'Very Bad',
    [AirQualityLevel.HEAVILY_POLLUTED]: 'Severe',
    [AirQualityLevel.SEVERELY_POLLUTED]: 'Harmful',
  },
};
