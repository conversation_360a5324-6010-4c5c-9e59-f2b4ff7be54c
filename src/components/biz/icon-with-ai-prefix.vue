<script setup lang="ts">
import { computed } from 'vue';
import { isTeacher } from '@/helps/is-teacher';


const fillColor = computed(() => {
  return isTeacher() ? '#365AA4' : '#4CAF4B';
});
</script>


<!-- eslint-disable max-len -->
<template>
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g id="Group 28">
      <path
        id="Star 1"
        d="M11.577 5.18445L13.4468 10.2376L18.5 12.1074L13.4468 13.9772L11.577 19.0303L9.70715 13.9772L4.65398 12.1074L9.70715 10.2376L11.577 5.18445Z"
        :fill="fillColor"
      />
      <path
        id="Star 3"
        d="M4.65381 0.969727L5.77571 4.0016L8.80762 5.12349L5.77571 6.24539L4.65381 9.27726L3.5319 6.24539L0.5 5.12349L3.5319 4.0016L4.65381 0.969727Z"
        :fill="fillColor"
      />
    </g>
  </svg>
</template>
