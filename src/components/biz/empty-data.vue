<script setup lang="ts">
defineOptions({
  name: 'EmptyData',
});

defineProps<{
  text?: string;
}>();
</script>

<template>
  <div class="empty-data">
    <img
      src="@/assets/img/no-data.png"
      alt=""
    >
    <div class="empty-tip">
      {{ text || $t('common.noData') }}
    </div>
  </div>
</template>


<style lang="less" scoped>
.empty-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  img {
    width: 160px;
    height: 187px;
  }

  .empty-tip {
    margin-top: 30px;
    color: #555;
    font-size: 15px;
    text-align: center;
  }
}
</style>
