<script lang="ts">
export default {
  name: 'BottomAction',
};
</script>


<template>
  <div class="bottom-action">
    <div class="bottom-action-wrapper">
      <slot />
    </div>
  </div>
</template>


<style lang="less" scoped>
.bottom-action {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  box-sizing: border-box;
  width: 100%;
  padding-bottom: 10px;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 10px);
  padding-bottom: calc(env(safe-area-inset-bottom) + 10px);
  background-color: #fff;

  .bottom-action-wrapper {
    display: flex;
    flex-direction: row;
    gap: 20px;
    box-sizing: border-box;
    width: 100%;
    padding: 10px 16px;

    :deep(.van-button) {
      white-space: nowrap;
    }
  }
}
</style>
