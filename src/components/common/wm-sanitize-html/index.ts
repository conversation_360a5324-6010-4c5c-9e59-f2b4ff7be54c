import { h, defineComponent } from 'vue';
import sanitize from 'sanitize-html';

import { allowedTags, allowedAttributes } from './sanitize-config';


export default defineComponent({
  name: 'WmSanitizeHtml',

  props: {
    innerHtml: {
      type: String,
      default: '',
    },

    tag: {
      type: String,
      default: 'div',
    },
  },

  setup(props) {
    return () => h(props.tag, {
      innerHTML: sanitize(props.innerHtml, {
        allowedTags,
        allowedAttributes,
      }),
    });
  },
});
