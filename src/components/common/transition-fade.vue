<script lang="ts">
export default {
  name: 'TransitionFade',
};
</script>


<template>
  <transition
    name="fade-transition"
    mode="out-in"
    appear
  >
    <slot />
  </transition>
</template>


<style lang="less" scoped>
.fade-transition-enter,
.fade-transition-leave-to {
  opacity: 0;
}

.fade-transition-enter-active {
  transition: all 0.2s ease-out;
}

.fade-transition-leave-active {
  transition: all 0.1s ease-in;
}
</style>
