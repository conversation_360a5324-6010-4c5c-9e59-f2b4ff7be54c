<script setup lang="ts">
import { Button, Popup } from 'vant';

defineOptions({
  name: 'PopupConfirmCancel',
});

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },

  loading: {
    type: Boolean,
    default: false,
  },

  text: {
    type: String,
    default: '',
  },

  title: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['onConfirm', 'onCancel', 'update:show']);

function onCancel() {
  if (props.loading) return;

  emit('update:show', false);
  emit('onCancel');
}
</script>

<template>
  <Popup
    :show="show"
    class="popup-confirm-cancel"
    :class="{ 'popup-confirm-cancel-with-title': title }"
    :close-on-click-overlay="!loading"
    @click-overlay="onCancel"
  >
    <div
      v-if="title"
      class="title"
    >
      {{ title }}
    </div>
    <div
      v-if="!$slots.default"
      class="text"
      :class="{ 'text-with-title': title }"
    >
      {{ text }}
    </div>

    <slot v-else />

    <div class="btn-container">
      <Button
        class="btn wm"
        plain
        type="default"
        :disabled="loading"
        @click="onCancel"
      >
        {{ $t('common.actions.cancel') }}
      </Button>

      <Button
        class="btn wm"
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="$emit('onConfirm')"
      >
        {{ $t('common.actions.confirm') }}
      </Button>
    </div>
  </Popup>
</template>


<style lang="less" scoped>
.popup-confirm-cancel {
  box-sizing: border-box;
  width: 300px;
  padding: 40px 24px 30px;
  border-radius: 5px;
  box-shadow: 0 5px 15px -5px #0000000f;

  &-with-title {
    padding-top: 32px;
  }

  .title {
    margin-bottom: 8px;
    color: #000000d9;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
  }

  .text {
    margin-bottom: 35px;
    color: #000000d9;
    font-size: 16px;
    line-height: 25px;
    text-align: center;

    &-with-title {
      margin-bottom: 21px;
      color: #545454;
      font-size: 14px;
      line-height: 20px;
    }
  }

  .btn-container {
    display: flex;
    justify-content: space-between;

    .btn {
      width: 116px;
      height: 37px;
    }
  }
}
</style>
