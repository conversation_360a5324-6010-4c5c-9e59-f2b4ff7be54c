<script lang="ts" setup>
import { computed } from 'vue';

import WmLoadingIcon from './wm-loading-icon.vue';

defineOptions({
  name: 'WmLoading',
});

interface Props {
  border?: boolean;
}
const props = defineProps<Props>();

const classes = computed(() => ({
  border: props.border,
}));
</script>


<template>
  <div
    class="wm-loading"
    :class="classes"
  >
    <WmLoadingIcon class="wm-loading-icon" />
  </div>
</template>


<style scoped>
.wm-loading {
  display: inline-block;
  box-sizing: border-box;
  width: 66px;
  height: 66px;
  padding: 22px 0;
  overflow: hidden;
}

.wm-loading.border {
  background-color: #fff;
  border: solid #e3e3e3 1px;
  border-radius: 5px;
}

.wm-loading-icon {
  display: block;
  margin: 0 auto;
  animation: 0.5s cubic-bezier(0.65, 0.05, 0.36, 1) infinite alternate spin;
}

.wm-loading-text {
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
}

@keyframes spin {
  100% {
    transform: rotate(45deg);
  }
}
</style>
