<script lang="ts">
import { Popup } from 'vant';
import { defineComponent } from 'vue';
import WmLoading from './wm-loading.vue';


export default defineComponent({
  name: 'ToastLoading',

  components: {
    [Popup.name]: Popup,
    WmLoading,
  },
});
</script>


<template>
  <van-popup
    :value="true"
    :overlay-style="{ backgroundColor: 'transparent' }"
    :style="{ backgroundColor: 'transparent' }"
  >
    <WmLoading />
  </van-popup>
</template>
