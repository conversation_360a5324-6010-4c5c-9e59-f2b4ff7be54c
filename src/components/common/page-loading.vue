<script setup lang="ts">
import WmLoading from './wm-loading.vue';
</script>

<template>
  <div class="page-loading">
    <WmLoading class="loading" />
  </div>
</template>


<style lang="less" scoped>
.page-loading {
  position: absolute;
  inset: 0;
  z-index: 2000;
  text-align: center;
  vertical-align: middle;
  background-color: #fff8;

  .loading {
    position: absolute;
    top: 40%;
    left: calc(50% - 33px);
    z-index: 1500;
  }
}
</style>
