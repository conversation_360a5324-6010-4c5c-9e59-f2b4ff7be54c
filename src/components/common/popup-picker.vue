<script setup lang="ts">
import { Popup, Picker } from 'vant';

import type { PickerConfirmEventParams, PickerColumn } from 'vant';

defineOptions({
  name: 'PopupPicker',
});

interface PopupPickProps<T = string[] | number[], P = unknown> {
  show: boolean;
  pickerValues: T;
  columns?: PickerColumn;
  loading?: boolean;
  title?: string;
  payload?: P;
  columnsFieldNames?: {
    text: string;
    value: string;
  };
}

const props = withDefaults(defineProps<PopupPickProps>(), {
  show: false,
  pickerValues: () => [],
  columns: () => [],
  loading: false,
  title: '',
  payload: null,
  columnsFieldNames: () => ({
    text: 'text',
    value: 'value',
  }),

});
const emit = defineEmits(['update:show', 'update:pickerValues']);
function onConfirm({ selectedValues }: PickerConfirmEventParams) {
  emit('update:show', false);

  if (!props.loading) {
    emit('update:pickerValues', selectedValues);
  }
}
</script>

<template>
  <Popup
    :show="show"
    :title="title"
    overlay-class="wm"
    position="bottom"
    safe-area-inset-bottom
    v-bind="$attrs"
    @update:show="$emit('update:show', $event)"
  >
    <Picker
      class="wm"
      :model-value="pickerValues"
      :title="title"
      :loading="loading"
      :readonly="loading"
      :columns="columns"
      :columns-field-names="columnsFieldNames"
      @cancel="$emit('update:show', false)"
      @confirm="onConfirm"
    />

    <div
      v-if="!columns?.length && !loading"
      class="empty-data"
    >
      {{ $t('common.noData') }}
    </div>
  </Popup>
</template>


<style lang="less">
.empty-data {
  position: absolute;
  top: 50%;
  left: 50%;
  color: #555;
  transform: translateX(-50%);
}
</style>
