<script lang="ts" setup>
import { Popup } from 'vant';

import { ref, onMounted } from 'vue';

const props = defineProps({
  duration: {
    type: Number,
    default: 0,
  },
  title: {
    type: String,
    default: null,
  },
  message: {
    type: String,
    default: null,
  },
});

const emit = defineEmits(['hide']);

const model = ref(true);


onMounted(() => {
  setTimeout(() => {
    model.value = false;
    emit('hide');
  }, props.duration);
});
</script>


<template>
  <Popup
    v-model:show="model"
    class="toast-error-popup"
  >
    <div class="toast-error-wrap">
      <div class="body">
        <img
          class="ico-error"
          src="@/assets/img/icon-fail.png"
        >
        <div class="title">
          {{ $props.title }}
        </div>
        <div
          v-if="$props.message"
          class="message"
        >
          {{ $props.message }}
        </div>
      </div>
    </div>
  </Popup>
</template>


<style lang="less">
.toast-error-popup {
  background-color: transparent;
}

.toast-error-wrap {
  box-sizing: border-box;
  width: 284px;

  .body {
    padding: 46px 20px;
    color: #222;
    font-weight: 400;
    font-size: 17px;
    text-align: center;
    background-color: #fff;
    border-radius: 5px;

    .ico-error {
      width: 35px;
    }

    .title {
      margin-top: 10px;
    }

    .message {
      margin-top: 10px;
      color: #555;
      font-size: 15px;
    }
  }
}
</style>
