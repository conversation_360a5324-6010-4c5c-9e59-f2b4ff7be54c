<script lang="ts">
import { Popup } from 'vant';

import { defineComponent, ref, onMounted } from 'vue';


export default defineComponent({
  name: 'ToastSuccess',

  components: {
    [Popup.name]: Popup,
  },

  props: {
    duration: {
      type: Number,
      default: 0,
    },
    title: {
      type: String,
      default: null,
    },
  },

  emits: ['hide'],

  setup(props, { emit }) {
    const model = ref(true);


    onMounted(() => {
      setTimeout(() => {
        model.value = false;
        emit('hide');
      }, props.duration);
    });


    return {
      model,
    };
  },
});
</script>


<template>
  <van-popup
    v-model:show="model"
    class="toast-success-popup"
  >
    <div class="toast-success-wrap">
      <div class="body">
        <img
          class="ico-success"
          src="@/assets/img/icon-success.png"
        >
        <div>{{ $props.title }}</div>
      </div>
    </div>
  </van-popup>
</template>


<style lang="less">
.van-popup.toast-success-popup {
  display: flex;
  justify-content: center;
  background-color: transparent;
}

.toast-success-wrap {
  box-sizing: border-box;
  width: 100vw;
  padding: 0 38px;

  .body {
    padding: 46px 20px;
    color: #222;
    font-weight: 400;
    font-size: 17px;
    text-align: center;
    background-color: #fff;
    border-radius: 5px;

    .ico-success {
      width: 35px;
    }

    .title {
      margin-top: 10px;
    }
  }
}
</style>
