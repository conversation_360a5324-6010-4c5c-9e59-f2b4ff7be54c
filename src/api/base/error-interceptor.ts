import eventBus from '@/utils/event-bus';

export function errorInterceptor(error) {
  if ([
    'ACCOUNT_DISABLE',
    'ACCOUNT_FREEZE_OR_DEACTIVATE',
    'ACCOUNT_EXCEPTION',
    'ERRCODE_ERROR_OF_UPDATING_SCEPTER',
    'ACCESS_CHECK',
    'ERRCODE_AUTHORIZATION_NULL_AND_VOID',
    'ERRCODE_ACCESS_CARD_IS_INVALID_OR_EXPIRED',
  ].includes(error.code)) {
    eventBus.emit('invaildAccessTokenError');
    throw error;
  }

  throw error;
}
