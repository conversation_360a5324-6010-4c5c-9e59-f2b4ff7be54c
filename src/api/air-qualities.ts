import { dateFormatSTZ } from '@/helps/date';
import { CommonModelApi } from './common/common-model-api';
import { ApiDate } from '@/config/api';
import { AirQualityVO } from '@/pages/index/teacher-index/helps/type';
import namespaceT from '@/helps/namespace-t';


export class AirQualitiesApi<T = unknown> extends CommonModelApi<T> {
  url() {
    return '/air-qualities';
  }

  defaultParams() {
    return {
      callerType: 'APP',
    };
  }

  async send(): Promise<T> {
    const res = await super.send() as AirQualityVO;
    const t = namespaceT('dateFormat');

    return {
      ...res,
      monitoringTime: dateFormatSTZ(res.monitoringTime, t('date'), ApiDate.FULL_DATE_TIME_WITH_TIME_ZONE),
    } as T;
  }
}
