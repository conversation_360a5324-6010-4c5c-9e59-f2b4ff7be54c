import { CommonModelApi } from '../common/common-model-api';
import { getAppType } from '@/helps/app-type';
import { ServiceVO } from '@/pages/index/teacher-index/helps/type';
import { AppType } from '@/config/api';


const appType = getAppType();

export class WithAiSearchApi<T> extends CommonModelApi<T> {
  url() {
    return '/project-categories/services/ai';
  }

  method(): string {
    return 'POST';
  }

  defaultData() {
    return {
      type: 'APP',
      appType,
    };
  }


  async send() {
    const res = await super.send() as ServiceVO[];
    const newRes = res.filter((item) => {
      const { code, isWorkbench } = item;

      // 特殊处理：IOS端不需要显示蓝牙设定应用
      if (appType === AppType.IOS && code === 'S-WM-PUNCH-APP') {
        return false;
      }

      // 过滤掉不显示在工作台的应用
      return isWorkbench === 1;
    }).map((item) => ({ ...item, enName: item.name }));

    return newRes as T;
  }
}
