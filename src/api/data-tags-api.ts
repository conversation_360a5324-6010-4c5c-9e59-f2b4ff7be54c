import { CommonApi } from '@/api/common/common-api';
import { valueByLocale } from '@/helps/locale';


export class DataTagsApi extends CommonApi {
  url() {
    return 'datas/tags';
  }

  async send() {
    const { model } = await super.send();
    if (!Array.isArray(model)) {
      return [];
    }

    return model.map((item) => {
      const { code, name, enName } = item;
      const label = valueByLocale(name, enName);

      return {
        ...item,
        label,
        value: code,
      };
    });
  }
}
