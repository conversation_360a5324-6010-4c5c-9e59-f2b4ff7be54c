import _ from 'lodash';
import { INFO_API_SALT } from '@/config/api';
import { CommonListApi } from '@/api/common/common-list-api';
import { ActivityInfo } from '@/pages/index/teacher-index/helps/type';


export class NewsInformationList<PERSON>pi extends CommonListApi<ActivityInfo[]> {
  constructor() {
    super({
      baseURL: `${process.env.INFO_API_BASE_URL}/api/v1`,
      salt: INFO_API_SALT,
    });
  }

  defaultParams() {
    return {
      offsetStart: 1,
      maxPageItems: 3,
      username: native.user.getUsername(),
      category: '',
      keywords: '',
      importanceLevel: '',
    };
  }

  url() {
    return 'article/getArticleList/';
  }

  async send() {
    const res = await this.request({
      method: this.method().toUpperCase(),
      data: this.targetData,
      url: this.url(),
      params: this.targetParams,
    });
    const data = _.get(res, 'list', []);
    const total: number = _.get(res, 'total', 0);
    return {
      data,
      total,
    };
  }
}
