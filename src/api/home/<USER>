import { SCHEDULE_API_SALT } from '@/config/api';
import { CommonModelApi } from '@/api/common/common-model-api';


export class LatestScheduleMessageApi extends CommonModelApi<unknown> {
  constructor() {
    super({
      baseURL: process.env.SCHEDULE_API_BASE_URL,
      salt: SCHEDULE_API_SALT,
    });
  }

  url() {
    return '/events/events/by-date';
  }

  defaultParams() {
    return {
      dataSource: 0,
    };
  }
}
