import { getAppType } from '@/helps/app-type';
import { CommonModelApi } from '../common/common-model-api';
import { ServiceCategory } from '@/pages/index/teacher-index/helps/type';
import { AppType } from '@/config/api';


const appType = getAppType();

export class HomeApplicationListApi<T = ServiceCategory> extends CommonModelApi<T> {
  url() {
    return '/project-categories/cache';
  }

  defaultParams() {
    return {
      type: 'APP',
      appType,
    };
  }

  async send(): Promise<T> {
    const res = await super.send() as ServiceCategory;
    const { projectCategoryList } = res;

    const newProjectCategoryList = projectCategoryList.map((projectCategory) => {
      const { serviceList } = projectCategory;
      const newServiceList = serviceList.filter((service) => {
        const { code, isWorkbench } = service;

        // 特殊处理：IOS端不需要显示蓝牙设定应用
        if (appType === AppType.IOS && code === 'S-WM-PUNCH-APP') {
          return false;
        }

        // 过滤掉不显示在工作台的应用
        return isWorkbench === 1;
      });
      return {
        ...projectCategory,
        serviceList: newServiceList,
      };
    }).filter((projectCategory) => projectCategory.serviceList.length > 0);


    return {
      ...res,
      projectCategoryList: newProjectCategoryList,
    } as T;
  }
}
