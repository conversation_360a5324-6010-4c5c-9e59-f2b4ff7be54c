import { ACTIVITY_API_SALT } from '@/config/api';
import { CommonListApi } from '@/api/common/common-list-api';
import { ActivityInfo } from '@/pages/index/teacher-index/helps/type';


export class CampusActivityListApi extends CommonListApi<ActivityInfo[]> {
  constructor() {
    super({
      baseURL: process.env.ACTIVITY_API_BASE_URL,
      salt: ACTIVITY_API_SALT,
    });
  }

  url() {
    return '/activitie-mobiles';
  }

  defaultParams() {
    return {
      offsetStart: 1,
      maxPageItems: 3,
    };
  }
}
