// import { CommonListApi } from '@/api/common/common-list-api';
// import { MessageInfo } from '@/pages/index/teacher-index/helps/type';


// export class LatestTodoMessageApi extends CommonListApi<MessageInfo> {
//   constructor() {
//     super({
//       baseURL: process.env.TODO_API_BASE_URL,
//     });
//   }

//   url() {
//     return '/item-mobiles';
//   }

//   // title: '授課：商業平台戰略',
//   // time: '上午10:00 - 12:00',
//   // url: '',
//   // address: 'N501',
// }

import { CommonModelApi } from '@/api/common/common-model-api';
import { MessageInfo } from '@/pages/index/teacher-index/helps/type';


export class LatestTodoMessageApi extends CommonModelApi<MessageInfo> {
  url() {
    return '/latest-todo-message';
  }

  async send(): Promise<MessageInfo> {
    // const res = await super.send();
    // return res;

    return {
      title: '授課：商業平台戰略',
      time: '上午10:00 - 12:00',
      address: 'N501',
      planId: '1',
      relateType: 'S-WM-SCHEDULE-NEW-APP',
      id: 1,
    };
  }
}
