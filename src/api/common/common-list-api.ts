import _ from 'lodash';
import { paginationParams } from '@/helps/api';
import { CommonApi } from './common-api';


export class CommonListApi<T = unknown> extends CommonApi {
  set params(value) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    super.params = paginationParams(value);
  }

  async send() : Promise<CommonApiListData<T>> {
    const res = await super.send();
    const data = _.get(res, 'data', []);
    const total: number = _.get(res, 'total', 0);
    return {
      data,
      total,
    };
  }
}
