/**
 * 判断当前环境是否为安卓浏览器。
 * @returns {boolean} 如果是安卓浏览器返回 true，否则返回 false。
 */
export function isInAndroid(): boolean {
  const ua = window.navigator.userAgent;
  return ua.indexOf('Android') > -1 || ua.indexOf('Adr') > -1;
}

/**
 * 判断当前环境是否为非 iOS 浏览器。
 * @returns {boolean} 如果不是 iOS 浏览器返回 true，否则返回 false。
 */
export function isInIos(): boolean {
  const regx = /iPhone|iPad|iPod|iOS/i;
  return regx.test(window.navigator.userAgent);
}

/**
 * 判断当前环境是否在微信浏览器内。
 * @returns {boolean} 如果在微信浏览器内返回 true，否则返回 false。
 */
export function isInWechat(): boolean {
  const regx = /MicroMessenger/i;
  return regx.test(window.navigator.userAgent);
}
