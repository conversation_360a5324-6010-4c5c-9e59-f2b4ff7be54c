export enum NativeEvent {
  NATIVE_READY = 'nativeready', // 原生接口已可用
  IDENTIFIER_INVALID = 'identifierinvalid', // 身份校验不通过
  HEADER_CLICKED = 'headerclicked', // 头部下拉菜单点击
  PUSH_CLICKED = 'pushclicked', // 远程推送消息点击
  MESSAGE_CLICKED = 'messageclicked', // 消息列表点击
  BACKWARD = 'backward', // 点击导航栏返回按钮
  WEBVIEW_RESUME = 'webviewresume', // 页面恢复
  SERVICE_CLICKED = 'serviceclicked', // 服务点击
  ON_BACK_PRESSED = 'onbackpressed', // 安卓物理返回键
}
