import { UserCategory } from "@/config/user-category";
import { AirQualityLevel } from "@/consts/air-quality-level";
import { AppTarget } from "@/consts/app-target";
import { YoN } from "@/consts/y-o-n";

export interface AppVersion {
  /** 系統類型 [IOS，ANDROID] */
  appType: string;
  /** 版本編碼 */
  code: string;
  /** 更新內容中文 */
  content: string;
  /** 更新內容英文 */
  contentEn: string;
  /** 下載地址 */
  downloadUrl: string;
  /** id */
  id: number;
  /** 強制更新 [0:否，1:是] */
  isForceUpdate: 0 | 1;
  /** 版本號 */
  sn: string;
  /** 升級提示中文 */
  updateTips: string;
  /** 升級提示英文 */
  updateTipsEn: string;
  /** 用戶分類 [STUDENT:學生，TEACHER:教職工] */
  userType: UserCategory;
}


export interface ServiceVO {
  /** 應用版本信息 */
  appVersion?: AppVersion; // AppVersion 接口需要另外定義
  /** 應用編碼 */
  code: string;
  /** 實際url（具體項目域名，用於直接跳轉） */
  directUrl?: string;
  /** 英文應用名稱 */
  enName: string;
  /** 應用圖標地址 */
  icon: string;
  /** id */
  id?: number;
  /** 是否接入幫助中心(app)，[0：否，1：是] */
  isAccessHelp?: 0 | 1;
  /** 是否用瀏覽器打開(app)，[0：否，1：是] */
  isBrowserApp?: 0 | 1;
  /** 是否接入数据授权2 [0:否，1:是] */
  isPermissionCtrl?: 0 | 1;
  /** 是否學生APP端 [0:否，1:是] */
  isStudentApp?: 0 | 1;
  /** 是否學生PC端 [0:否，1:是] */
  isStudentPc?: 0 | 1;
  /** 是否教職員APP端 [0:否，1:是] */
  isTeacherApp?: 0 | 1;
  /** 是否教職員PC端 [0:否，1:是] */
  isTeacherPc?: 0 | 1;
  /** 是否顯示在工作台(app)，[0：否，1：是] */
  isWorkbench?: 0 | 1;
  /** 應用名稱 */
  name: string;
  /** 打開方式[_self：當前頁面，_blank：新頁面，INTERNAL_APP：內部應用，EXTERNAL_APP：外部應用，EXTERNAL_BROWSER：外部瀏覽器，PUSH_MSG：僅推送消息] */
  target?: AppTarget;
  /** 應用訪問url */
  url?: string;
  /** 版本編碼 */
  versionCode?: string;
  /** 版本編號 */
  versionSn?: string;
}

export interface ProjectCategoryList {
  /** 英文名稱 */
  enName: string;
  /** id */
  id: number;
  /** 中文名稱 */
  name: string;
  /** 服務列表 */
  serviceList: ServiceVO[];
}


export interface ServiceCategory {
  cacheTime: string;
  projectCategoryList: ProjectCategoryList[];
}


export interface AirQualityVO {
  /** 空氣質量指數 */
  aqi: string;
  /** co浓度(APP、PC頭部調用時，會返回單位) */
  co: string;
  /** 控制是否顯示 */
  displayInd: YoN;
  /** 空氣質量等級（枚举AirQualityLevelEnum：優 EXCELLENT、良好 GOOD、輕度污染 MILDLY_POLLUTED、中度污染 MODERATELY_POLLUTED、重度污染 HEAVILY_POLLUTED、嚴重污染 SEVERELY_POLLUTED） */
  levelName: AirQualityLevel | null;
  /** 主要污染物-直接返回中英文，前端不需要对字典code */
  mainPollutant: string;
  /** 主要污染物-浓度(APP、PC頭部調用時，會返回單位) */
  mainPollutantConcentration: string;
  /** 监测站id */
  monitoringSiteId: number;
  /** 监测站名称 */
  monitoringSiteName: string;
  /** 监测时间 */
  monitoringTime: string;
  /** nox浓度(APP、PC頭部調用時，會返回單位) */
  nox: string;
  /** o3浓度(APP、PC頭部調用時，會返回單位) */
  o3: string;
  /** pm10浓度(APP、PC頭部調用時，會返回單位) */
  pm10: string;
  /** pm2.5浓度(APP、PC頭部調用時，會返回單位) */
  pm25: string;
  /** so2浓度(APP、PC頭部調用時，會返回單位) */
  so2: string;
}

// 首页消息模块
export interface MessageInfo {
  id: number;
  title: string;
  time: string;
  address: string;
  planId: string;
  relateType?: string;
}

export interface ActivityInfo {
  id: string;
  title: string;
  time: string;
  photo: string;
  url: string;
  address: string;
}
