import namespaceT from '@/helps/namespace-t';
import { YoN } from '@/consts/y-o-n';

import type { AirQualityVO, ProjectCategoryList, ServiceCategory, MessageInfo } from './type';


export function createApplicationModel(): ServiceCategory {
  return {
    cacheTime: '',
    projectCategoryList: [],
  };
}


export function createHitApplicationModel(): ProjectCategoryList {
  const t = namespaceT('home.title');

  return {
    enName: t('recentlyUsed'),
    id: 0,
    name: t('recentlyUsed'),
    serviceList: [],
  };
}


export function createAirQualityModel(): AirQualityVO {
  return {
    aqi: '',
    levelName: null,
    displayInd: YoN.N,
    mainPollutant: '',
    mainPollutantConcentration: '',
    monitoringSiteId: null,
    monitoringSiteName: '',
    monitoringTime: '',
    pm10: '',
    pm25: '',
    co: '',
    nox: '',
    o3: '',
    so2: '',
  };
}

export function createMessageInfoModel(): MessageInfo {
  return {
    id: null,
    planId: '',
    title: '',
    time: '',
    address: '',
    relateType: '',
  };
}
