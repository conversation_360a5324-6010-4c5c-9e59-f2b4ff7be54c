import CloudyImg from '@/assets/img/weather/bg-cloudy.png';
import RainyImg from '@/assets/img/weather/bg-rainy.png';
import SunnyImg from '@/assets/img/weather/bg-sunny.png';

import { WeatherCode } from '@/consts/weather';
import namespaceT from '@/helps/namespace-t';

export const DefaultWeather = {
  key: WeatherCode.SUNNY,
  backgroundImage: SunnyImg,
  background: 'linear-gradient(180deg, #1480FF 0%, #F1F2F6 100%)',
};

export const WeatherBgs = [
  DefaultWeather,
  {
    key: WeatherCode.RAINY,
    backgroundImage: RainyImg,
    background: 'linear-gradient(180deg, #0857D8 0%, #F1F2F6 100%)',
  },
  {
    key: WeatherCode.CLOUDY,
    backgroundImage: CloudyImg,
    background: 'linear-gradient(180deg, #9CB0D9 0%, #F1F2F6 100%)',
  },
];

export function getWeatherText(code) {
  const t = namespaceT('home.weather');
  const mapper = new Map([
    [WeatherCode.SUNNY, t('sunny')],
    [WeatherCode.RAINY, t('rainy')],
    [WeatherCode.CLOUDY, t('cloudy')],
  ]);

  return mapper.get(code) || t('sunny');
}
