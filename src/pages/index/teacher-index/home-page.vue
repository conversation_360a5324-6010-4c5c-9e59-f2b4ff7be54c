<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { PullRefresh } from 'vant';
import _ from 'lodash';

import PageLoading from '@/components/common/page-loading.vue';
import WeatherBg from './components/weather-bg.vue';
import HeaderActions from './components/header-actions.vue';
import WeatherAndAirQuality from './components/weather-and-air-quality.vue';
import CommonlyUsedFunctions from './components/commonly-used-functions.vue';
import ApplicationBlock from './components/application-block.vue';
import MessageBlock from './components/message-block.vue';
import ActivityBlock from './components/activity-block.vue';

import { LatestScheduleMessageService } from '@/services/latest-schedule-message';
import { CampusActivityListService } from '@/services/campus-activity-list';
import { NewsInformationListService } from '@/services/news-information-list';
import { HomeServiceHitsApi } from '@/api/home/<USER>';
import { LatestTodoMessageApi } from '@/api/home/<USER>';

import { AppStorageKey } from '@/config/app-storage';
import { YoN } from '@/consts/y-o-n';
import { ServiceCode } from '@/consts/service-code';
import { useCommonConfig } from '@/uses/use-common-config';
import { getAppStorageItem, setAppStorageItem } from '@/helps/app-storage';
import { isNothing } from '@/utils/is-nothing';
import { sleep } from '@/utils/sleep';
import { isInIos } from '@/utils/browser';
import namespaceT from '@/helps/namespace-t';
import { createHitApplicationModel, createMessageInfoModel } from './helps/model';

import { getScheduleDetailUrl } from './helps/url';
import type { MessageInfo, ServiceVO } from './helps/type';
import { openToastError } from '@/helps/toast';


const COMMON_SERVICE_LEN = 7; // 常用功能的长度
const t = namespaceT('home');
const hitApplicationModel = ref(createHitApplicationModel());
const {
  loadCommonConfig,
  model: commonConfigModel,
  isShowScan,
} = useCommonConfig();
const pageLoading = ref(false);
const refreshLoading = ref(false);
const weather = ref('sunny');
const minTemperature = ref(16);
const maxTemperature = ref(23);
const todoInfoModel = reactive(createMessageInfoModel());
const scheduleInfoModel = ref(null);
const campusActivityModel = ref([]);
const newsInformationModel = ref([]);

// 待辦消息
async function loadTodoMessageInfo() {
  const api = new LatestTodoMessageApi();
  const res = await api.send();

  Object.assign(todoInfoModel, res);
  setAppStorageItem(AppStorageKey.TO_DO_MESSAGES, todoInfoModel);
}

// 日程消息
async function loadScheduleMessageInfo() {
  const service = new LatestScheduleMessageService();
  const res = await service.fetch();

  scheduleInfoModel.value = res;
  setAppStorageItem(AppStorageKey.SCHEDULE_MESSAGES, scheduleInfoModel.value);
}

// 校園活動
async function loadCampusActivity() {
  const service = new CampusActivityListService();
  const res = await service.fetch();

  campusActivityModel.value = res;
  setAppStorageItem(AppStorageKey.CAMPUS_ACTIVITY_LIST, campusActivityModel.value);
}

// 新聞資訊
async function loadNewsInformation() {
  const service = new NewsInformationListService();
  const res = await service.fetch();

  newsInformationModel.value = res;
  setAppStorageItem(AppStorageKey.NEWS_INFORMATION_LIST, newsInformationModel.value);
}

// 最近使用
async function loadHitApplicationList() {
  const api = new HomeServiceHitsApi<ServiceVO[]>();
  const res = await api.send();

  if (!isNothing(res)) {
    let serviceList = _.cloneDeep(res);
    // 《蓝牙设定》应用特殊处理逻辑
    // 该应用为原生应用，只存在Android端
    if (isInIos()) {
      serviceList = serviceList.filter((app) => ![ServiceCode.S_WM_PUNCH_APP].includes(app.code));
    }

    hitApplicationModel.value.serviceList = serviceList.splice(0, COMMON_SERVICE_LEN);
    setAppStorageItem(AppStorageKey.RECENTLY_USED_APPLICATIONS, serviceList);
  }
}

async function onRefresh() {
  if (refreshLoading.value) {
    return;
  }

  refreshLoading.value = true;
  const requestBeforeTimestamp = new Date().getTime();

  try {
    await Promise.all([
      loadHitApplicationList(),
      loadCommonConfig(),
      loadTodoMessageInfo(),
      loadScheduleMessageInfo(),
      loadCampusActivity(),
      loadNewsInformation(),
    ]);
    const requestAfterTimeStamp = new Date().getTime();

    // 是否需要等待 1 秒
    const waitTime = 1000 - (requestAfterTimeStamp - requestBeforeTimestamp);
    if (waitTime > 0) {
      await sleep(waitTime);
    }
  } catch (error) {
    openToastError(error);
  } finally {
    refreshLoading.value = false;
  }
}

function onApplicationItemClick(application: ServiceVO) {
  const len = hitApplicationModel.value.serviceList.length;
  const index = hitApplicationModel.value.serviceList.findIndex((app) => app.code === application.code);

  if (index > -1) {
    hitApplicationModel.value.serviceList.splice(index, 1);
  }

  if (len === COMMON_SERVICE_LEN && index === -1) {
    hitApplicationModel.value.serviceList.pop();
  }

  hitApplicationModel.value.serviceList.unshift(application);
}

function convertPageDataByAppStorage() {
  // 处理常用功能
  const commonlyUsedFunctionCacheData = getAppStorageItem(AppStorageKey.COMMONLY_USED_FUNCTIONS);
  if (!isNothing(commonlyUsedFunctionCacheData)) {
    commonConfigModel.value = commonlyUsedFunctionCacheData;
  }

  // 处理最近使用
  const hitApplicationCacheData = getAppStorageItem(AppStorageKey.RECENTLY_USED_APPLICATIONS);
  if (!isNothing(hitApplicationCacheData)) {
    hitApplicationModel.value.serviceList = hitApplicationCacheData;
  }

  // 待辦消息
  const todoMessageCacheData = getAppStorageItem(AppStorageKey.TO_DO_MESSAGES);
  if (!isNothing(todoMessageCacheData)) {
    Object.assign(todoInfoModel, todoMessageCacheData);
  }

  // 日程消息
  const scheduleMessageCacheData = getAppStorageItem(AppStorageKey.SCHEDULE_MESSAGES);
  if (!isNothing(scheduleMessageCacheData)) {
    Object.assign(scheduleInfoModel, scheduleMessageCacheData);
  }

  loadHitApplicationList();
  loadCommonConfig();
  loadTodoMessageInfo();
  loadScheduleMessageInfo();
  loadCampusActivity();
  loadNewsInformation();
}


function initPage() {
  const isLoaded = getAppStorageItem(AppStorageKey.USER_IS_LOADED) === YoN.Y;

  // 当前用户已加载过首页，直接使用缓存数据
  if (isLoaded) {
    convertPageDataByAppStorage();
    return;
  }

  // 首次加载首页
  pageLoading.value = true;

  Promise.all([
    loadHitApplicationList(),
    loadCommonConfig(),
    loadTodoMessageInfo(),
    loadScheduleMessageInfo(),
    loadCampusActivity(),
    loadNewsInformation(),
  ])
    .catch((error) => {
      openToastError(error);
    })
    .finally(() => {
      setAppStorageItem(AppStorageKey.USER_IS_LOADED, YoN.Y);
      pageLoading.value = false;
    });
}

onMounted(() => {
  initPage();

  native.navigator.setNavigationBarHidden(true, false);

  // 关闭长按图片出现下载图片功能
  native.settings.setImageSettings?.({
    longPressGestureRecognizerSwitch: 0,
  });
});

// TODO: 校园活动点击查看更多
function onCampusActivityViewMore() {}

// TODO: 新闻资讯点击查看更多
function onNewsInformationViewMore() {}

// TODO: 点击查看待办消息
function onViewTodoMessage() {}

// 点击查看日程消息
function onViewScheduleMessage(data: MessageInfo) {
  native.navigator.launchWebView(getScheduleDetailUrl(data.planId, data.id, data.relateType));
}

// 点击查看活动详情
function onCampusActivityItemClick(item) {
  native.navigator.v2.launchService({
    serviceCode: process.env.ACTIVITY_SERVICE_CODE,
    eventArgs: {
      dataId: item.id,
      category: 'VIEW_EVENT_DETAIL',
    },
  });
}

// 点击查看资讯详情
function onNewsInformationItemClick(item) {
  native.navigator.v2.launchService({
    serviceCode: process.env.INFO_SERVICE_CODE,
    eventArgs: {
      dataId: item.id,
      category: 'VIEW_INFO',
    },
  });
}
</script>


<template>
  <PageLoading v-if="pageLoading" />

  <div class="home-page">
    <WeatherBg
      class="weather-body"
      :weather="weather"
    >
      <!-- 操作栏 -->
      <HeaderActions
        :is-show-scan="isShowScan"
        @on-application-item-click="onApplicationItemClick"
      />

      <PullRefresh
        class="application-content"
        :model-value="refreshLoading"
        @refresh="onRefresh"
      >
        <div class="application-content-inner">
          <!-- 天气 & 空气质量 -->
          <WeatherAndAirQuality
            :weather="weather"
            :refresh-loading="refreshLoading"
            :min-temperature="minTemperature"
            :max-temperature="maxTemperature"
          />

          <!-- 常用功能 -->
          <CommonlyUsedFunctions
            :list="commonConfigModel.operatingList"
            :refresh-loading="refreshLoading"
          />

          <!-- 最近使用 -->
          <ApplicationBlock
            v-if="hitApplicationModel.serviceList.length > 0"
            :list="hitApplicationModel"
            @on-click="onApplicationItemClick"
          />

          <!-- 待办消息 临时隐藏 -->
          <MessageBlock
            v-if="false"
            :title="t('title.todoMessage')"
            :model="todoInfoModel"
            @click="onViewTodoMessage"
          />

          <!-- 日程消息 -->
          <MessageBlock
            v-if="scheduleInfoModel"
            :title="t('title.scheduleMessage')"
            :model="scheduleInfoModel"
            @click="onViewScheduleMessage"
          />

          <!-- 大學活動 -->
          <ActivityBlock
            :model="campusActivityModel"
            :title="t('title.campusActivity')"
            show-meta-icon
            @on-view-more="onCampusActivityViewMore"
            @on-click-item="onCampusActivityItemClick"
          />

          <!-- 新聞資訊 -->
          <ActivityBlock
            :model="newsInformationModel"
            :title="t('title.newsInformation')"
            @on-view-more="onNewsInformationViewMore"
            @on-click-item="onNewsInformationItemClick"
          />
        </div>
      </PullRefresh>
    </WeatherBg>
  </div>
</template>


<style lang="less" scoped>
@import "@/styles/variables.less";

.home-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: 20px;
  overflow: hidden;
  background-color: @page-bg;

  .weather-body {
    padding-top: 14px;
    padding-bottom: 20px;
  }

  .application-content {
    flex: 1;
    height: calc(100% - 52px - 34px);
    overflow: auto;

    .application-content-inner {
      position: relative;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
