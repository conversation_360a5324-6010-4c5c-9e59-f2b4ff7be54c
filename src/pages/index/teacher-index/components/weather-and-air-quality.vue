<script setup lang="ts">
import { onMounted, reactive, watch } from 'vue';

import AirQualities from './air-qualities.vue';

import { AirQualitiesApi } from '@/api/air-qualities';

import { createAirQualityModel } from '../helps/model';
import { isNothing } from '@/utils/is-nothing';
import { isY } from '@/helps/y-o-n';

import type { AirQualityVO } from '../helps/type';
import { getWeatherText } from '../helps/weather';


const airQualityModel = reactive(createAirQualityModel());

interface Props {
  refreshLoading: boolean;
  weather: string;
  minTemperature: number;
  maxTemperature: number;
}

const props = defineProps<Props>();

async function loadAirQuality() {
  try {
    const api = new AirQualitiesApi<AirQualityVO>();
    const res = await api.send();
    if (!isNothing(res)) {
      Object.assign(airQualityModel, res);
    }
  } catch {
    // TODO: 错误被拦截，但不进行任何操作
  }
}

watch(() => props.refreshLoading, (newVal) => {
  if (newVal) {
    loadAirQuality();
  }
});

onMounted(() => {
  loadAirQuality();
});
</script>


<template>
  <div class="weather-and-air-quality">
    <div class="weather mr-8">
      {{ minTemperature }}
      ~
      {{ maxTemperature }}
      ℃
      <span class="ml-4">{{ getWeatherText(props.weather) }}</span>
    </div>
    <AirQualities
      v-if="isY(airQualityModel.displayInd)"
      :info="airQualityModel"
    />
  </div>
</template>


<style lang="less" scoped>
.weather-and-air-quality {
  display: flex;
  align-items: center;
  min-height: 26px;
  margin-top: 20px;
  margin-bottom: 12px;
  padding: 0 16px;
}

.weather {
  color: #fff;
  font-size: 16px;
  line-height: 1;
}
</style>
