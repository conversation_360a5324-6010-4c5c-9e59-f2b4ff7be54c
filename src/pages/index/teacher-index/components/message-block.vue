<script lang="ts" setup>
import type { MessageInfo } from '../helps/type';

withDefaults(defineProps<{
  model: MessageInfo;
  title: string;
}>(), {});

const emits = defineEmits<{
  'click': [MessageInfo];
}>();
</script>


<template>
  <div
    class="message-block"
    @click="emits('click', model)"
  >
    <div class="block-title">
      {{ title }}
    </div>

    <div class="message-info">
      <div class="message-title text-ellipsis">
        {{ model.title }}
      </div>
      <div class="message text-ellipsis">
        {{ `${model.time} ${model.address}` }}
      </div>
    </div>
  </div>
</template>


<style lang="less" scoped>
.message-block {
  margin: 8px 16px;
  padding: 10px 12px;
  background-color: #fff;
  border-radius: 12px;
}

.block-title {
  padding-left: 17px;
  color: #1480ff;
  font-weight: 500;
  font-size: 12px;
  background: url("@/assets/img/home/<USER>") no-repeat left center;
}

.message-info {
  margin-top: 2px;
  padding-left: 17px;
  color: #555;
  font-weight: 500;
  font-size: 14px;

  .message {
    margin-top: 2px;
    color: #888;
    font-weight: 400;
    font-size: 12px;
    line-height: 1.3;
  }
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
