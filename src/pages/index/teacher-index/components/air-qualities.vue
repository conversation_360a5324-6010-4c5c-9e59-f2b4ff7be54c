<script setup lang="ts">
import _ from 'lodash';
import { computed } from 'vue';

import { getAirQualityLevelI18nText } from '@/helps/i18n/air-quality-level';

import type { AirQualityVO } from '../helps/type';

interface Props {
  info: AirQualityVO;
}

const props = defineProps<Props>();

const airQualityLevelClass = computed(() => {
  return props.info.levelName?.toLowerCase().replace(/_/g, '-');
});

function onShowAirQuality() {
  native.business.showAirQualityPage(_.cloneDeep(props.info));
}
</script>


<template>
  <div
    class="air-qualities"
    :class="airQualityLevelClass"
    @click="onShowAirQuality"
  >
    <div class="icon" />
    <div class="num">
      {{ info.aqi }}
    </div>
    <div class="text">
      {{ getAirQualityLevelI18nText( info.levelName) }}
    </div>
  </div>
</template>


<style lang="less" scoped>
@import '@/styles/variables.less';

.air-qualities {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  height: 26px;
  padding: 0 12px 0 8px;
  color: @white;
  font-weight: 400;
  font-size: 12px;
  line-height: 26px;
  background-color: fade(#fff, 10%);
  border-radius: 13px;

  // &.excellent {
  //   background-color: #64C828;
  // }

  // &.good {
  //   background-color: #F2D155;
  // }

  // &.mildly-polluted {
  //   background-color: #EA9655;
  // }

  // &.moderately-polluted {
  //   background-color: #E46A63;
  // }

  // &.heavily-polluted {
  //   background-color: #9A72B3;
  // }

  // &.severely-polluted {
  //   background-color: #986D78;
  // }

  .icon {
    width: 18px;
    height: 16px;
    background: url('@/assets/img/air-qualities.png') no-repeat center / 100%;
  }

  .num {
    margin: 0 2px;
  }
}
</style>
