<script setup lang="ts">
import { toRef } from 'vue';

import { useCommonlyUsedFunctions, type CommonlyUsedFunction } from '@/uses/use-commonly-used-functions';

import type { Operating } from '@/types/common-config';


interface Props {
  list: Operating[];
  refreshLoading: boolean;
}

const props = defineProps<Props>();
const { commonlyUsedFunctionList } = useCommonlyUsedFunctions(toRef(props, 'list'));


function onClick(item: CommonlyUsedFunction) {
  item.run();
}

</script>


<template>
  <template v-if="commonlyUsedFunctionList.length > 0">
    <div class="commonly-used-functions">
      <div class="function-list">
        <div
          v-for="item in commonlyUsedFunctionList"
          :key="item.code"
          class="function"
          @click="onClick(item)"
        >
          <div class="function-icon">
            <img
              :src="item.icon"
              class="icon"
              alt="function-icon"
              draggable="false"
            >
          </div>

          <div class="text">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </template>
</template>


<style lang="less" scoped>
@import '@/styles/variables.less';

.commonly-used-functions {
  margin: 0 16px;
  margin-top: 12px;
  padding: 16px 16px 18px;
  background-color: #fff;
  border-radius: 12px;

  .function-list {
    display: flex;
    justify-content: space-between;

    .function {
      display: flex;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      width: 25%;
      text-align: center;
      user-select: none;

      &-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: 50px;
        height: 50px;
        padding: 8px;
        background-color: #f6fafb;;
        border-radius: 50%;

        .icon {
          width: 34px;
          height: 34px;
        }
      }

      .text {
        margin-top: 6px;
        color: @black-5;
        font-size: 12px;
        line-height: normal;
        word-break: break-word;
      }
    }
  }
}
</style>
