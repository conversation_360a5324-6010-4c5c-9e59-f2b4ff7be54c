<script setup lang="ts">
import ApplicationItem from '@/components/biz/application-item.vue';

import namespaceT from '@/helps/namespace-t';

import ViewMoreIcon from '@/assets/img/home/<USER>';

const ta = namespaceT('home.action');
const tt = namespaceT('pageTitle');

function onViewMore() {
  window.native.navigator.push('service.html', tt('service'));
}

const viewMoreApplication = {
  enName: ta('moreEn'),
  icon: ViewMoreIcon,
  name: ta('moreZh'),
  code: 'view-more',
};
</script>


<template>
  <ApplicationItem
    :application-item="viewMoreApplication"
    @on-click="onViewMore"
  />
</template>
