<script lang="ts" setup>
import { WeatherCode } from '@/consts/weather';

withDefaults(defineProps<{
  weather: string;
}>(), {});


function getWeatherClass(weather) {
  switch (weather) {
    case WeatherCode.SUNNY:
      return 'bg-sunny';

    case WeatherCode.RAINY:
      return 'bg-rainy';

    case WeatherCode.CLOUDY:
      return 'bg-cloudy';

    default:
      return 'bg-sunny';
  }
}
</script>


<template>
  <div
    class="weather-bg-wrapper"
    :class="getWeatherClass(weather)"
  >
    <div class="inner-content">
      <slot />
    </div>
  </div>
</template>


<style lang="less" scoped>
.weather-bg-wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  &.bg-sunny {
    background: url('@/assets/img/weather/bg-sunny.png') no-repeat 100% top / cover;
  }

  &.bg-rainy {
    background: url('@/assets/img/weather/bg-rainy.png') no-repeat 100% top / cover;
  }

  &.bg-cloudy {
    background: url('@/assets/img/weather/bg-cloudy.png') no-repeat 100% top / cover;
  }
}

.inner-content {
  position: relative;
  z-index: 1;
  height: 100%;
}
</style>
