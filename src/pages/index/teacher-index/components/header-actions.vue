<script setup lang="ts">
import { Badge } from 'vant';

import scanOptions from '@/config/scan-options';

import namespaceT from '@/helps/namespace-t';
import type { ServiceVO } from '../helps/type';


interface Props {
  isShowScan: boolean;
}

defineProps<Props>();
defineEmits<{
  'on-application-item-click': [ServiceVO];
}>();


const tt = namespaceT('pageTitle');

function onScan() {
  native.navigator.dismiss();
  native.qrcode.v2.scan({
    actions: scanOptions,
  });
}

function onSearch() {
  window.native.navigator.push('search.html', '');
}

function onMessage() {
  native.business.showMessagePage();
}

function onAddTransaction() {
  window.native.navigator.push('transaction-add.html', tt('transactionAdd'));
}
</script>


<template>
  <div class="header-actions">
    <!-- 扫一扫 -->
    <div
      v-if="isShowScan"
      class="scan action-item"
      @click="onScan"
    >
      <img
        class="icon"
        src="@/assets/img/header/scan.svg"
        alt="scan"
      >
    </div>

    <div class="right-extra">
      <!-- 搜索 -->
      <div
        class="action-item dark mr-6"
        @click="onSearch"
      >
        <img
          class="icon"
          src="@/assets/img/header/search.svg"
          alt="search"
        >
      </div>

      <!-- 消息 -->
      <Badge dot>
        <div
          class="action-item mr-6"
          @click="onMessage"
        >
          <img
            class="icon"
            src="@/assets/img/header/message.svg"
            alt="message"
          >
        </div>
      </Badge>

      <!-- 新增 -->
      <div
        class="action-item"
        @click="onAddTransaction"
      >
        <img
          class="icon"
          src="@/assets/img/header/add.svg"
          alt="create"
        >
      </div>
    </div>
  </div>
</template>


<style lang="less" scoped>
.header-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  height: 52px;
  padding: 10px 16px;

  .action-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: fade(#fff, 80%);
    border-radius: 50%;

    &.debug .icon {
      width: 22px;
    }

    &.dark {
      background-color: #1f65e0;
    }
  }

  .right-extra {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-end;
  }

  :deep(.van-badge--dot) {
    top: 4px;
    right: 4px;
    width: 6px;
    height: 6px;
    border: 1px solid #fff;
  }
}
</style>
