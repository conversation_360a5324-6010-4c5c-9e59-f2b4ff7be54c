<script setup lang="ts">
import ApplicationItem from '@/components/biz/application-item.vue';
import ViewMoreApp from './view-more-app.vue';

import type { ProjectCategoryList, ServiceVO } from '../helps/type';


interface Props {
  list: ProjectCategoryList;
}

withDefaults(defineProps<Props>(), {
});
defineEmits<{
  'on-click': [ServiceVO];
}>();
</script>


<template>
  <div class="wrapper-application-block">
    <!-- 內容 -->
    <div class="application-list">
      <template
        v-for="applicationItem of list.serviceList"
        :key="applicationItem.id"
      >
        <ApplicationItem
          :application-item="applicationItem"
          @on-click="$emit('on-click', applicationItem)"
        />
      </template>
      <ViewMoreApp />
    </div>
  </div>
</template>


<style lang="less" scoped>
.wrapper-application-block {
  margin-top: 8px;
  padding: 0 16px;

  .application-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px 0;
    padding: 10px 16px 18px;
    background-color: #fff;
    border-radius: 12px;
  }
}
</style>
