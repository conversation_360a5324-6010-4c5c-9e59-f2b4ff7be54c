<script lang="ts" setup>
import type { ActivityInfo } from '../helps/type';

import ActivityItem from './activity-item.vue';

withDefaults(defineProps<{
  model: Array<ActivityInfo>;
  title: string;
  showMetaIcon?: boolean;
}>(), {
  showMetaIcon: false,
});

defineEmits<{
  'on-view-more': null;
  'on-click-item': null;
}>();

</script>


<template>
  <div class="activity-block">
    <div class="title-bar">
      {{ title }}

      <img
        src="@/assets/img/home/<USER>"
        alt="arrow-right"
        class="arrow-right"
        @click="$emit('on-view-more')"
      >
    </div>

    <div class="list-wrapper">
      <ActivityItem
        v-for="item in model"
        :key="`activity-${item.id}`"
        class="list-item"
        :model="item"
        :show-meta-icon="showMetaIcon"
        @click="$emit('on-click-item', item)"
      />
    </div>
  </div>
</template>


<style lang="less" scoped>
.activity-block {
  margin: 8px 16px;
  padding: 16px 16px 6px;
  background-color: #fff;
  border-radius: 12px;
}

.title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
  color: #222;
  font-weight: 500;
  font-size: 16px;
  line-height: 1;
}
</style>
