<script lang="ts" setup>
import type { ActivityInfo } from '../helps/type';

withDefaults(defineProps<{
  model: ActivityInfo;
  showMetaIcon?: boolean;
}>(), {
  showMetaIcon: false,
});
</script>


<template>
  <div
    class="activity-item"
  >
    <div
      v-if="model.photo"
      class="photo mr-8"
    >
      <img
        :src="model.photo"
        alt="activity photo"
      >
    </div>
    <div class="meta">
      <div
        class="title"
      >
        {{ model.title }}
      </div>
      <div class="time">
        <img
          v-if="showMetaIcon"
          src="@/assets/img/home/<USER>"
          alt="icon"
          class="icon mr-4"
        >
        {{ model.time }}
      </div>
      <div class="address">
        <img
          v-if="showMetaIcon"
          src="@/assets/img/home/<USER>"
          alt="icon"
          class="icon mr-4"
        >
        {{ model.address }}
      </div>
    </div>
  </div>
</template>


<style lang="less" scoped>
.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 10px 0;

  &:not(:last-child) {
    border-bottom: 1px solid #f5f5f5;
  }

  .photo {
    width: 60px;
    overflow: hidden;
    font-size: 0;
    border-radius: 6px;

    > img {
      width: 100%;
      height: auto;
      object-fit: cover;
    }
  }
}

.meta {
  flex: 1;

  .title {
    display: -webkit-box;
    margin-bottom: 12px;
    overflow: hidden;
    color: #222;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.36;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;
    text-overflow: ellipsis;
  }

  .time,
  .address {
    display: flex;
    align-items: center;
    color: #888;
    font-weight: 400;
    font-size: 12px;
    line-height: 1;
  }

  .address {
    margin-top: 6px;
  }
}
</style>
