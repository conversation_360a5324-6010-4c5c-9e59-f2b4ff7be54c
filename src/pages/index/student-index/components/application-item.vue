<script setup lang="ts">
import { toRef } from 'vue';

import { AppTarget } from '@/consts/app-target';
import { useUploadClickApplication } from '@/uses/use-upload-click-application';
import { useAppTarget } from '@/uses/use-app-target';
import { useLongPress } from '@/uses/use-long-press';
import { currentLocalIsEn } from '@/helps/locale';

import type { ServiceVO } from '../helps/type';


interface Props {
  applicationItem: ServiceVO;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'on-click': [];
}>();


const { uploadClickApplication } = useUploadClickApplication();
const { getAppJumpFn } = useAppTarget(toRef(props, 'applicationItem'));


function onLongPressCallback() {
  /** 注：只有內部應用才可進行強制更新 */
  if (props.applicationItem.target !== AppTarget.INTERNAL_APP) {
    return;
  }

  native.business.forceUpdateService({
    code: props.applicationItem.code,
  });
}

function onClick() {
  const jumpFn = getAppJumpFn(props.applicationItem.target);
  jumpFn?.();
  uploadClickApplication(props.applicationItem.code);
  emit('on-click');
}

const { onTouchStart, onTouchMove, onTouchEnd } = useLongPress({
  isNeedClick: true,
  onLongPressCallback,
  onClick,
});
</script>


<template>
  <div
    class="application-item"
    @touchstart="onTouchStart"
    @touchmove="onTouchMove"
    @touchend="onTouchEnd"
  >
    <img
      :src="applicationItem.icon"
      class="application-item-icon"
      alt="application-icon"
      draggable="false"
    >

    <div
      class="text"
      :class="{ en: currentLocalIsEn() }"
    >
      {{ $judgeLanguage(applicationItem.name, applicationItem.enName) }}
    </div>
  </div>
</template>


<style lang="less" scoped>
@import '@/styles/variables.less';

.application-item {
  box-sizing: border-box;
  width: 25%;
  padding: 10px 2px 16px;
  text-align: center;
  user-select: none;

  &-icon {
    width: 44px;
    height: 44px;
    object-fit: contain;
  }

  .text {
    margin-top: 8px;
    color: @black-2;
    font-size: 14px;
    line-height: normal;
    word-break: break-word;

    &.en {
      font-size: 12px;
    }
  }
}
</style>
