<script setup lang="ts">
import TitleBar from './title-bar.vue';


interface Props {
  title: string;
}
defineProps<Props>();
</script>


<template>
  <div class="wrapper-application-block">
    <!-- 標題 -->
    <TitleBar :title="title" />

    <!-- 內容 -->
    <div class="application-list">
      <slot />
    </div>
  </div>
</template>


<style lang="less" scoped>
.wrapper-application-block {
  padding: 17px 20px 3px;

  .application-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 9px;
  }
}
</style>
