<script setup lang="ts">
import { onMounted, reactive, toRef, watch } from 'vue';

import TitleBar from './title-bar.vue';
import DividerLine from './divider-line.vue';
import AirQualities from './air-qualities.vue';

import { AirQualitiesApi } from '@/api/air-qualities';

import namespaceT from '@/helps/namespace-t';
import { createAirQualityModel } from '../helps/model';
import { isY } from '@/helps/y-o-n';
import { useCommonlyUsedFunctions, type CommonlyUsedFunction } from '@/uses/use-commonly-used-functions';
import { isNothing } from '@/utils/is-nothing';

import type { AirQualityVO } from '../helps/type';
import type { Operating } from '@/types/common-config';


const airQualityModel = reactive(createAirQualityModel());

interface Props {
  list: Operating[];
  refreshLoading: boolean;
}

const props = defineProps<Props>();
const { commonlyUsedFunctionList } = useCommonlyUsedFunctions(toRef(props, 'list'));

const t = namespaceT('home.title');

async function loadAirQuality() {
  try {
    const api = new AirQualitiesApi<AirQualityVO>();
    const res = await api.send();
    if (!isNothing(res)) {
      Object.assign(airQualityModel, res);
    }
  } catch {
    // TODO: 错误被拦截，但不进行任何操作
  }
}

function onClick(item: CommonlyUsedFunction) {
  item.run();
}

watch(() => props.refreshLoading, (newVal) => {
  if (newVal) {
    loadAirQuality();
  }
});

onMounted(() => {
  loadAirQuality();
});
</script>


<template>
  <template v-if="commonlyUsedFunctionList.length > 0">
    <div class="commonly-used-functions">
      <!-- 標題 -->
      <TitleBar :title="t('commonlyUsed')">
        <template #right-extra>
          <div
            v-if="isY(airQualityModel.displayInd)"
            class="air-qualities"
          >
            <AirQualities :info="airQualityModel" />
          </div>
        </template>
      </TitleBar>

      <div class="function-list">
        <div
          v-for="item in commonlyUsedFunctionList"
          :key="item.code"
          class="function"
          @click="onClick(item)"
        >
          <img
            :src="item.icon"
            class="function-icon"
            alt="function-icon"
            draggable="false"
          >

          <div class="text">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>

    <DividerLine />
  </template>
</template>


<style lang="less" scoped>
@import '@/styles/variables.less';

.commonly-used-functions {
  margin-top: 22px;
  padding: 0 16px 3px;

  .function-list {
    display: flex;
    padding: 16px 0 13px;


    .function {
      box-sizing: border-box;
      width: 25%;
      text-align: center;
      user-select: none;

      &-icon {
        width: 40px;
        height: 40px;
      }

      .text {
        margin-top: 5px;
        color: @black-5;
        font-size: 13px;
        line-height: normal;
        word-break: break-word;
      }
    }
  }
}
</style>
