<script setup lang="ts">
import { Popover } from 'vant';
import { computed, onBeforeMount, onMounted, ref, toRef } from 'vue';

import AppSearch from './app-search.vue';

import scanOptions from '@/config/scan-options';
import { OperatingCode } from '@/consts/operating-code';
import { getOperatingCodeI18nText } from '@/helps/i18n/operating-code';
import { useCreateOptions, type CreateOption } from '@/uses/use-create-options';
import { useAppTarget } from '@/uses/use-app-target';
import { isTeacher } from '@/helps/is-teacher';
import { NativeEvent } from '@/config/native-event';

import type { ProjectCategoryList, ServiceVO } from '../helps/type';


interface Props {
  isShowScan: boolean;
  isShowDebug: boolean;
  applicationList: ProjectCategoryList[];
}

const props = defineProps<Props>();
defineEmits<{
  'on-application-item-click': [ServiceVO];
}>();

const isShowNewPopover = ref(false);

const { createOptions } = useCreateOptions(toRef(props, 'applicationList'));
const { getCreateOptionJumpFn } = useAppTarget();

const isShowCreateBtn = computed(() => {
  return createOptions.value.length > 0 && isTeacher();
});

function onScan() {
  native.qrcode.v2.scan({
    actions: scanOptions,
  });
}

function onDebug() {
  native.business.showDebugPage();
}

function onCreate() {
  isShowNewPopover.value = true;
}

function onClickCreateOption(option: CreateOption) {
  const jumpFn = getCreateOptionJumpFn(option);
  jumpFn?.();
}

function onWebviewResume() {
  if (isShowNewPopover.value) {
    isShowNewPopover.value = false;
  }
}

onMounted(() => {
  native.events.addEventListener(NativeEvent.WEBVIEW_RESUME, onWebviewResume);
});

onBeforeMount(() => {
  native.events.removeEventListener(NativeEvent.WEBVIEW_RESUME, onWebviewResume);
});
</script>


<template>
  <div class="app-header">
    <div class="logo-container">
      <img
        class="logo"
        src="@/assets/img/wemust-logo.png"
        alt="logo"
        draggable="false"
      >

      <div class="right-extra">
        <!-- DEBUG -->
        <div
          v-if="isShowDebug"
          class="item"
          @click="onDebug"
        >
          <img
            class="icon"
            src="@/assets/img/header/debug.png"
            alt="scan"
          >
          <span class="name">{{ getOperatingCodeI18nText(OperatingCode.DEBUG) }}</span>
        </div>

        <!-- 扫一扫 -->
        <div
          v-if="isShowScan"
          class="item"
          @click="onScan"
        >
          <img
            class="icon"
            src="@/assets/img/header/scan.png"
            alt="scan"
          >
          <span class="name">{{ getOperatingCodeI18nText(OperatingCode.SCAN_ACCESS) }}</span>
        </div>

        <!-- 新增 -->
        <Popover
          v-model:show="isShowNewPopover"
          :actions="createOptions"
          class="new-popover"
          trigger="manual"
          overlay
          overlay-class="wm"
          show-arrow
          placement="bottom-end"
          :offset="[6, 10]"
          @select="onClickCreateOption"
        >
          <template #reference>
            <div
              v-if="isShowCreateBtn"
              class="item"
              @click="onCreate"
            >
              <img
                class="icon"
                src="@/assets/img/header/create.png"
                alt="scan"
              >
              <span class="name">{{ $t('home.action.create') }}</span>
            </div>
          </template>
        </Popover>
      </div>
    </div>

    <AppSearch @on-application-item-click="$emit('on-application-item-click', $event)" />
  </div>
</template>


<style lang="less">
@import '@/styles/variables.less';

.app-header {
  flex-shrink: 0;
  height: 110px;

  .logo-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    height: 44px;
    padding: 0 13px 0 20px;

    .logo {
      width: 106px;
      height: 24px;
    }

    .right-extra {
      display: flex;
      align-items: center;

      .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-left: 22px;
        color: @black-5;
        font-size: 9px;
        line-height: 1;

        .icon {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}

.new-popover {
  overflow: unset;
  border-radius: 8px;

  .van-popover__content {
    background: none;
    border-radius: 0;

    .van-popover__action {
      box-sizing: border-box;
      width: auto;
      height: unset;
      padding: 10px 24px;

      .van-icon {
        width: 26px;
        height: 26px;
        margin-right: 12px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .van-popover__action-text {
        color: #292929;
        font-size: 14px;
        line-height: 22px;

        &::after {
          display: none;
        }
      }

      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 50px;
        height: 1px;
        background-color: #f2f2f2;
        transform: scaleY(0.5);
        content: '';
      }

      &:first-child {
        padding-top: 13px;
      }

      &:last-child {
        padding-bottom: 13px;

        &::after {
          display: none;
        }
      }
    }
  }
}
</style>
