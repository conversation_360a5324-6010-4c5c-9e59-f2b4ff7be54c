<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { Field } from 'vant';

import PageLoading from '@/components/common/page-loading.vue';
import EmptyData from '@/components/biz/empty-data.vue';
import IconWithAiPrefix from '@/components/biz/icon-with-ai-prefix.vue';
import ApplicationItem from './application-item.vue';

import { WithAiSearchApi } from '@/api/search/ai-search';

import { NativeEvent } from '@/config/native-event';
import { isTeacher } from '@/helps/is-teacher';
import namespaceT from '@/helps/namespace-t';
import { currentLocalIsEn } from '@/helps/locale';
import { openToastError } from '@/helps/toast';

import type { FieldInstance } from 'vant';
import type { ServiceVO } from '../helps/type';


interface SearchModel {
  loading: boolean;
  error: boolean;
  finished: boolean;
  data: ServiceVO[];
}

defineEmits<{
  'on-application-item-click': [ServiceVO];
}>();

const searchInputRef = ref<FieldInstance>();
const t = namespaceT('home');
const te = namespaceT('apiError.aiSearch');

const isShow = ref<boolean>(false);
const keyword = ref('');
const searchModel = ref<SearchModel>({
  loading: false,
  error: false,
  finished: false,
  data: [],
});
let requestFlag = 0;

async function fetchSearchAiApplication() {
  if (searchModel.value.loading) {
    return;
  }

  searchModel.value.loading = true;
  searchModel.value.error = false;
  searchModel.value.finished = false;

  requestFlag += 1;
  const currentFlag = requestFlag;

  try {
    const api = new WithAiSearchApi<ServiceVO[]>();
    api.data = {
      content: keyword.value,
    };
    const res = await api.send();

    if (currentFlag >= requestFlag) {
      searchModel.value.data = res;
      searchModel.value.finished = true;
    }
  } catch (error) {
    openToastError({ message: te('search') });
    searchModel.value.error = true;
    throw error;
  } finally {
    searchModel.value.loading = false;
  }
}

function onClickSearchInputParent() {
  if (isShow.value) {
    return;
  }

  isShow.value = true;
  searchInputRef.value?.focus();
}

function onSearch() {
  if (!isShow.value || !keyword.value) {
    isShow.value = true;
    searchInputRef.value?.focus();
    return;
  }

  fetchSearchAiApplication();
}

function onEnter() {
  if (!keyword.value) {
    searchInputRef.value?.focus();
    return;
  }

  fetchSearchAiApplication();
}

function onResetSearchModel() {
  searchModel.value.data = [];
  searchModel.value.error = false;
  searchModel.value.loading = false;
  searchModel.value.finished = false;
}

function onClear() {
  keyword.value = '';
  onResetSearchModel();
}


function onCancel() {
  if (searchModel.value.loading) {
    return;
  }

  isShow.value = false;
  keyword.value = '';
  onResetSearchModel();
}

function onBackPressCallback() {
  if (isShow.value) {
    isShow.value = false;
    keyword.value = '';
    onResetSearchModel();
  }
}

watch(() => isShow.value, () => {
  requestFlag += 1;
});

onMounted(() => {
  native.events.addEventListener(NativeEvent.ON_BACK_PRESSED, onBackPressCallback);
});

onBeforeUnmount(() => {
  native.events.removeEventListener(NativeEvent.ON_BACK_PRESSED, onBackPressCallback);
});
</script>


<template>
  <div
    class="app-search"
    :class="{ active: isShow }"
    @click="onClickSearchInputParent"
  >
    <div class="search-input-container">
      <div
        class="input-container"
        :class="{ teacher: isTeacher() }"
      >
        <div class="search-prefix">
          <IconWithAiPrefix class="icon-prefix" />
          <span class="text">{{ t('label.withAi') }}</span>
        </div>

        <Field
          ref="searchInputRef"
          v-model="keyword"
          class="search-input"
          type="search"
          clearable
          :readonly="searchModel.loading"
          :placeholder="t('placeholder.search')"
          @clear="onClear"
          @keypress.enter="onEnter"
        />

        <div
          class="search-button"
          :class="{ disabled: !keyword }"
          @click.stop="onSearch"
        />
      </div>

      <div
        class="cancel-btn"
        :class="{ active: isShow, en: currentLocalIsEn() }"
        @click.stop="onCancel"
      >
        {{ t('action.cancel') }}
      </div>
    </div>

    <div
      v-if="isShow"
      class="search-content"
    >
      <PageLoading v-if="searchModel.loading && !searchModel.error" />

      <EmptyData v-if="searchModel.data.length === 0 && searchModel.finished" />

      <template v-if="searchModel.data.length > 0">
        <div class="tip">
          {{ t('tip.search') }}
        </div>

        <div class="search-result">
          <ApplicationItem
            v-for="item of searchModel.data"
            :key="item.code"
            :application-item="item"
            @on-click="$emit('on-application-item-click', item)"
          />
        </div>
      </template>
    </div>
  </div>
</template>


<style lang="less" scoped>
@import '@/styles/variables.less';

.app-search {
  position: fixed;
  top: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  padding: 12px;
  padding-bottom: 0;
  transform: translate3d(0, 44px, 0);
  transition: all .3s;

  &.active {
    position: fixed;
    inset: 0;
    padding-top: 8px;
    background-color: @white;
    transform: translate3d(0, 0, 0);
  }

  .search-input-container {
    display: flex;

    .input-container {
      display: flex;
      flex: 1;
      align-items: center;
      box-sizing: border-box;
      min-width: 0;
      height: 54px;
      padding: 10px 12px;
      background-color: @white;
      border: 1px solid #eee;
      border-radius: 12px;
      box-shadow: 0 0 10px 0 fade(#000, 10);
      transition: all .3s;

      .search-prefix {
        position: relative;
        display: flex;
        flex-shrink: 0;
        align-items: center;
        padding-right: 12px;

        .icon-prefix {
          width: 18px;
          height: 18px;
          margin-right: 4px;
        }

        .text {
          color: @primary-green;
          font-weight: 500;
          font-size: 14px;
          font-style: italic;
          line-height: normal;
          letter-spacing: 0.28px;
        }

        &::after {
          position: absolute;
          top: -2px;
          right: 0;
          width: 1px;
          height: 22px;
          background-color: fade(#000, 8);
          content: '';
        }
      }

      .search-input {
        flex: 1;
        min-width: 0;
        padding: 0 4px 0 12px;

        &::after {
          display: none;
        }

        :deep(.van-field__control) {
          height: 22px;
          color: @black-1;
          font-size: 14px;

          &::placeholder {
            color: @black-8;
            font-size: 12px;
          }

          /* 隐藏 input[type="search"] 的清空按钮 */
          &::-webkit-search-cancel-button {
            -webkit-appearance: none;
            appearance: none;
          }
        }
      }

      .search-button {
        flex-shrink: 0;
        width: 32px;
        height: 32px;
        background: url('@/assets/img/icon-search.png') no-repeat center / 15px;

        &:active {
          opacity: 0.8;
        }

        &.disabled {
          cursor: not-allowed;

          &:active {
            opacity: 1;
          }
        }
      }

      &:focus-within {
        border-color: @primary-green;
        box-shadow: 0 0 10px 0 fade(@primary-green, 30);

      }

      &.teacher {
        &:focus-within {
          border-color: @primary;
          box-shadow: 0 0 10px 0 fade(@primary, 30);
        }

        .search-prefix {
          .text {
            color: @primary;
          }
        }
      }
    }

    .cancel-btn {
      flex-shrink: 0;
      box-sizing: border-box;
      width: 55px;
      height: 42px;
      margin-top: 6px;
      margin-right: -63px;
      margin-left: 8px;
      padding: 10px;
      color: @black-6;
      font-size: 16px;
      line-height: 22px;
      letter-spacing: 0.32px;
      background-color: @white;
      border-radius: 2px;
      visibility: hidden;
      opacity: 0;
      transition: all .2s;

      &.en {
        width: 75px;
        margin-right: -83px;
      }

      &.active {
        margin-right: 0;
        visibility: visible;
        opacity: 1;
        transition: opacity 0 .1s, margin-right 0.2s, visibility .2s;
      }
    }
  }

  .search-content {
    position: relative;
    flex: 1;
    min-width: 0;
    min-height: 0;
    padding: 20px 13px;
    overflow: auto;

    .tip {
      margin-bottom: 8px;
      color: @black-7;
      font-size: 12px;
      line-height: normal;
    }

    .search-result {
      display: flex;
      flex-wrap: wrap;

      .application {
        box-sizing: border-box;
        width: 25%;
        height: 100px;
        padding: 10px 10px 16px;
        text-align: center;

        &-icon {
          width: 40px;
          height: 44px;
        }

        .text {
          margin-top: 8px;
          overflow: hidden;
          color: @black-2;
          font-size: 14px;
          line-height: normal;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
