<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';

import type { ServiceCategory } from '@/pages/index/teacher-index/helps/type';
import { createApplicationModel } from '@/pages/index/teacher-index/helps/model';
import { isNothing } from '@/utils/is-nothing';
import {} from '@/helps/'

import PageLoading from '@/components/common/page-loading.vue';
import TitleBar from '@/components/common/title-bar.vue';
import MenuList from './components/menu-list.vue';

import { HomeApplicationListApi } from '@/api/home/<USER>';


const pageLoading = ref(false);
const applicationModel = reactive(createApplicationModel());


async function loadApplicationList() {
  const api = new HomeApplicationListApi<ServiceCategory>();
  const res = await api.send();

  if (!isNothing(res)) {
    Object.assign(applicationModel, res);
  }
}

async function initPage() {
  try {
    pageLoading.value = true;
    await loadApplicationList();
  } finally {
    pageLoading.value = false;
  }
}

onMounted(() => {
  initPage();
});
</script>


<template>
  <div class="transaction-add">
    <TitleBar />
    <div class="transaction-add-body">
      <PageLoading v-if="pageLoading" />
      <MenuList
        :application-list="applicationModel.projectCategoryList"
      />
    </div>
  </div>
</template>


<style scoped lang="less">
@import "@/styles/variables.less";


.transaction-add {
  height: 100%;
  background-color: @page-bg;

  .transaction-add-body {
    position: relative;
    height: calc(100% - 50px);
  }
}
</style>
