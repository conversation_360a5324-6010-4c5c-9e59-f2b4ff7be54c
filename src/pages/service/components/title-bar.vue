<script setup lang="ts">
interface Props {
  title: string;
}
defineProps<Props>();
</script>


<template>
  <div class="title-bar">
    <div class="left">
      {{ title }}
    </div>

    <slot name="right-extra" />
  </div>
</template>


<style lang="less" scoped>
@import '@/styles/variables.less';

.title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: @black-2;
  font-weight: 500;
  font-size: 16px;
  line-height: 1;

  .left {
    flex-shrink: 0;
  }
}
</style>
