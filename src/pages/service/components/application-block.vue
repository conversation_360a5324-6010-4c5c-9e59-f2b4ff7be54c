<script setup lang="ts">
import WrapperApplicationBlock from './wrapper-application-block.vue';
import DividerLine from './divider-line.vue';
import ApplicationItem from '@/components/biz/application-item.vue';

import type { ProjectCategoryList, ServiceVO } from '../helps/type';


interface Props {
  list: ProjectCategoryList;
  isShowDivider?: boolean;
}

withDefaults(defineProps<Props>(), {
  isShowDivider: true,
});
defineEmits<{
  'on-click': [ServiceVO];
}>();
</script>


<template>
  <WrapperApplicationBlock :title="$judgeLanguage(list.name, list.enName)">
    <template
      v-for="applicationItem of list.serviceList"
      :key="applicationItem.id"
    >
      <ApplicationItem
        :application-item="applicationItem"
        @on-click="$emit('on-click', applicationItem)"
      />
    </template>
  </WrapperApplicationBlock>

  <DividerLine v-if="isShowDivider" />
</template>


<style lang="less" scoped>
.wrapper-application-block {
  .application-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 9px;
  }
}
</style>
