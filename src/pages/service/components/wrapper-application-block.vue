<script setup lang="ts">
import TitleBar from './title-bar.vue';


interface Props {
  title?: string;
}

withDefaults(defineProps<Props>(), {
  title: '',
});
</script>


<template>
  <div class="wrapper-application-block">
    <!-- 標題 -->
    <TitleBar
      v-if="title"
      :title="title"
    />

    <!-- 內容 -->
    <div class="application-list">
      <slot />
    </div>
  </div>
</template>


<style lang="less" scoped>
.wrapper-application-block {
  padding: 16px 16px 20px;
  background-color: #fff;
  border-radius: 12px;

  .application-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px 0;
    margin-top: 12px;
  }
}
</style>
