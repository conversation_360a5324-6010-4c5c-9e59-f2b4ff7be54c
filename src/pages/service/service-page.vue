<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { PullRefresh } from 'vant';
import _ from 'lodash';

import ApplicationBlock from './components/application-block.vue';
import PageLoading from '@/components/common/page-loading.vue';

import { HomeApplicationListApi } from '@/api/home/<USER>';
import { HomeServiceHitsApi } from '@/api/home/<USER>';

import { AppStorageKey } from '@/config/app-storage';
import { YoN } from '@/consts/y-o-n';
import { ServiceCode } from '@/consts/service-code';
import { useCommonConfig } from '@/uses/use-common-config';
import { getAppStorageItem, setAppStorageItem } from '@/helps/app-storage';
import { createApplicationModel, createHitApplicationModel } from './helps/model';
import { isNothing } from '@/utils/is-nothing';
import { sleep } from '@/utils/sleep';
import { isInIos } from '@/utils/browser';

import type { ServiceCategory, ServiceVO } from './helps/type';


const applicationModel = reactive(createApplicationModel());
const hitApplicationModel = ref(createHitApplicationModel());
const {
  loadCommonConfig,
  model: commonConfigModel,
} = useCommonConfig();
const pageLoading = ref(false);
const refreshLoading = ref(false);


async function loadApplicationList() {
  const api = new HomeApplicationListApi<ServiceCategory>();
  const res = await api.send();

  if (!isNothing(res)) {
    Object.assign(applicationModel, res);
    setAppStorageItem(AppStorageKey.APPLICATION_LIST, res);
  }
}

async function loadHitApplicationList() {
  const api = new HomeServiceHitsApi<ServiceVO[]>();
  const res = await api.send();

  if (!isNothing(res)) {
    let serviceList = _.cloneDeep(res);
    // 《蓝牙设定》应用特殊处理逻辑
    // 该应用为原生应用，只存在Android端
    if (isInIos()) {
      serviceList = serviceList.filter((app) => ![ServiceCode.S_WM_PUNCH_APP].includes(app.code));
    }

    hitApplicationModel.value.serviceList = serviceList;
    setAppStorageItem(AppStorageKey.RECENTLY_USED_APPLICATIONS, serviceList);
  }
}

async function onRefresh() {
  if (refreshLoading.value) {
    return;
  }

  refreshLoading.value = true;
  const requestBeforeTimestamp = new Date().getTime();

  try {
    await Promise.all([loadApplicationList(), loadHitApplicationList(), loadCommonConfig()]);
    const requestAfterTimeStamp = new Date().getTime();

    // 是否需要等待 1 秒
    const waitTime = 1000 - (requestAfterTimeStamp - requestBeforeTimestamp);
    if (waitTime > 0) {
      await sleep(waitTime);
    }
  } catch {
    // TODO: 错误被拦截，但不进行任何操作
  } finally {
    refreshLoading.value = false;
  }
}

function onApplicationItemClick(application: ServiceVO) {
  const len = hitApplicationModel.value.serviceList.length;
  const index = hitApplicationModel.value.serviceList.findIndex((app) => app.code === application.code);

  if (index > -1) {
    hitApplicationModel.value.serviceList.splice(index, 1);
  }

  if (len === 8 && index === -1) {
    hitApplicationModel.value.serviceList.pop();
  }

  hitApplicationModel.value.serviceList.unshift(application);
}

function convertPageDataByAppStorage() {
  // 处理常用功能
  const commonlyUsedFunctionCacheData = getAppStorageItem(AppStorageKey.COMMONLY_USED_FUNCTIONS);
  if (!isNothing(commonlyUsedFunctionCacheData)) {
    commonConfigModel.value = commonlyUsedFunctionCacheData;
  }

  // 处理最近使用
  const hitApplicationCacheData = getAppStorageItem(AppStorageKey.RECENTLY_USED_APPLICATIONS);
  if (!isNothing(hitApplicationCacheData)) {
    hitApplicationModel.value.serviceList = hitApplicationCacheData;
  }

  // 处理应用列表
  const applicationListCacheData = getAppStorageItem(AppStorageKey.APPLICATION_LIST);
  if (!isNothing(applicationListCacheData)) {
    Object.assign(applicationModel, applicationListCacheData);
  }

  loadApplicationList();
  loadHitApplicationList();
  loadCommonConfig();
}


function initPage() {
  const isLoaded = getAppStorageItem(AppStorageKey.USER_IS_LOADED) === YoN.Y;

  // 当前用户已加载过首页，直接使用缓存数据
  if (isLoaded) {
    convertPageDataByAppStorage();
    return;
  }

  // 首次加载首页
  pageLoading.value = true;

  Promise.all([loadApplicationList(), loadHitApplicationList(), loadCommonConfig()])
    .catch(() => {
      // TODO: 错误被拦截，但不进行任何操作
    })
    .finally(() => {
      setAppStorageItem(AppStorageKey.USER_IS_LOADED, YoN.Y);
      pageLoading.value = false;
    });
}

onMounted(() => {
  initPage();

  // 关闭长按图片出现下载图片功能
  native.settings.setImageSettings?.({
    longPressGestureRecognizerSwitch: 0,
  });
});
</script>


<template>
  <PageLoading v-if="pageLoading" />

  <div class="home-page">
    <PullRefresh
      class="application-content"
      :model-value="refreshLoading"
      @refresh="onRefresh"
    >
      <!-- 最近使用 -->
      <ApplicationBlock
        v-if="hitApplicationModel.serviceList.length > 0"
        :list="hitApplicationModel"
        @on-click="onApplicationItemClick"
      />

      <!-- 应用列表 -->
      <ApplicationBlock
        v-for="(applicationBlock, index) of applicationModel.projectCategoryList"
        :key="applicationBlock.id"
        :list="applicationBlock"
        :is-show-divider="index !== applicationModel.projectCategoryList.length - 1"
        @on-click="onApplicationItemClick"
      />
    </PullRefresh>
  </div>
</template>


<style lang="less" scoped>
@import "@/styles/variables.less";

.home-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  overflow: hidden;
  background-color: @page-bg;

  .application-content {
    flex: 1;
    min-height: 0;
    overflow: auto;
  }
}
</style>
