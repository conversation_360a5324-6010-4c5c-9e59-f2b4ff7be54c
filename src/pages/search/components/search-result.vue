<script lang="ts" setup>
import ApplicationItem from '@/components/biz/application-item.vue';
import PageLoading from '@/components/common/page-loading.vue';
import EmptyData from '@/components/biz/empty-data.vue';
import ActivityItem from '@/pages/index/teacher-index/components/activity-item.vue';
import SearchResultTitle from './search-result-title.vue';

import type { ServiceVO, ActivityInfo } from '@/pages/index/teacher-index/helps/type';
import type { SearchResultType } from '../helps/type';
import namespaceT from '@/helps/namespace-t';


withDefaults(defineProps<{
  data: SearchResultType;
  loading?: boolean;
  finished?: boolean;
  error?: boolean;
}>(), {
  loading: false,
  finished: false,
  error: false,
});

defineEmits<{
  'on-application-item-click': [ServiceVO];
  'on-activity-item-click': [ActivityInfo];
}>();


const t = namespaceT('home');

</script>


<template>
  <div class="search-result">
    <PageLoading v-if="loading && !error" />

    <EmptyData v-if="data.service.length === 0 && finished" />

    <!-- 爲您找到以下服務 -->
    <template v-if="data.service.length > 0">
      <SearchResultTitle
        :title="t('tip.search')"
        :num="data.service.length"
      />

      <div class="application-result">
        <ApplicationItem
          v-for="item of data.service"
          :key="item.code"
          :application-item="item"
          @on-click="$emit('on-application-item-click', item)"
        />
      </div>
    </template>

    <!-- TODO:相關結果 -->
    <template v-if="data.relate.length > 0">
      <SearchResultTitle
        :title="t('tip.relate')"
        :num="data.relate.length"
      />

      <div class="relate-result">
        <ActivityItem
          v-for="item of data.relate"
          :key="item.id"
          :model="item"
          show-meta-icon
          class="relate-result-item"
          @on-click="$emit('on-activity-item-click', item)"
        />
      </div>
    </template>
  </div>
</template>


<style lang="less" scoped>
@import '@/styles/variables.less';

.search-result {
  height: 100%;
  overflow: auto;

  .application-result {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 32px;
  }

  .relate-result-item:not(:last-child) {
    border-bottom: 1px solid #eaeaea;
  }
}
</style>
