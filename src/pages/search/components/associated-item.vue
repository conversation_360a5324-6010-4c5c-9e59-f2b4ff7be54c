<script lang="ts" setup>
import { computed } from 'vue';

import WmSanitizeHtml from '@/components/common/wm-sanitize-html';

const props = defineProps<{ text: string; keyword: string }>();

const highlightedText = computed(() => {
  if (!props.keyword) return props.text;
  // Escape special regex characters in keyword
  const escapedKeyword = props.keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const regex = new RegExp(escapedKeyword, 'gi');
  return props.text.replace(regex, (match) => `<span class="highlight">${match}</span>`);
});
</script>


<template>
  <div class="associated-item">
    <img
      src="@/assets/img/home/<USER>"
      class="search-icon"
    >
    <WmSanitizeHtml
      :inner-html="highlightedText"
      class="associated-text"
    />
  </div>
</template>


<style lang="less" scoped>
.associated-item {
  display: flex;
  gap: 4px;
  align-items: center;
  padding: 16px 2px;

  &:not(:last-child) {
    border-bottom: 1px solid #eaeaea;
  }
}

.associated-text {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: #444;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  white-space: normal;
  text-overflow: ellipsis;

  :deep(.highlight) {
    color: #1480ff;
  }
}
</style>
