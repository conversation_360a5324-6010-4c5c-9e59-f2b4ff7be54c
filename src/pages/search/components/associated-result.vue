<script lang="ts" setup>
import AssociatedItem from './associated-item.vue';

defineProps<{ list: string[]; keyword: string }>();

interface EmitType {
  (e: 'on-search', keyword: string): void;
}
const emit = defineEmits<EmitType>();

function onClickAssociatedItem(item) {
  emit('on-search', item);
}
</script>

<template>
  <div>
    <AssociatedItem
      v-for="item in list"
      :key="item"
      :text="item"
      :keyword="keyword"
      @click="onClickAssociatedItem(item)"
    />
  </div>
</template>
