<script lang="ts" setup>
import { Button } from 'vant';
import { ref } from 'vue';

import namespaceT from '@/helps/namespace-t';


const t = namespaceT('home.placeholder');
const isPhonetic = defineModel<boolean>('isPhonetic');
const model = defineModel<string>();
const phoneticBtnRef = ref();
const isPressed = ref(false);
const isCancel = ref(false);

function onClickRightIcon(e) {
  e.stopPropagation();
  isPhonetic.value = false;
}

function onStopPropagation(e) {
  e.stopPropagation();
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function onClickTalkBtn() {
  console.log('model', model.value);
}

function onTouchStart() {
  isPressed.value = true;
  isCancel.value = false;
}

function onTouchMove(event) {
  const touch = event.touches[0];
  const rect = phoneticBtnRef.value.$el.getBoundingClientRect();
  isCancel.value = touch.clientY < rect.top;
}

function onTouchEnd() {
  isPressed.value = false;
}
</script>


<template>
  <div
    v-show="isPhonetic"
    class="phonetic-btn-wrapper"
  >
    <div class="phonetic-wrapper">
      <Button
        ref="phoneticBtnRef"
        class="phonetic-btn"
        :class="{
          'phonetic-btn--pressed': isPressed,
          'phonetic-btn--cancel': isCancel,
        }"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
      >
        {{ isPressed ? '' : t('pressToTalk') }}
        <img
          src="@/assets/img/home/<USER>"
          alt="microphone"
          class="right-icon"
          @click="onClickRightIcon"
          @touchstart="onStopPropagation"
        >
      </Button>
    </div>
  </div>
</template>


<style lang="less" scoped>
.phonetic-btn-wrapper {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 16px 12px 20px;
  background-color: transparent;
}

.phonetic-wrapper {
  height: 56px;
  overflow: hidden;
  border-radius: 60px;
  box-shadow: 0 -1px 3px 0 fade(#000, 6%), 0 6px 9px 0 fade(#000, 12%);
}

.phonetic-btn {
  width: 100%;
  height: 100%;
  padding: 2px 12px;
  color: #111;
  font-weight: 400;
  font-size: 15px;
  background-color: #fff;
  border: 0;

  &:active::before {
    opacity: 0;
  }

  :deep(.van-button__text) {
    position: relative;
    width: 100%;
  }

  &.phonetic-btn--pressed {
    background-color: #0086ff;
    background-image: url("@/assets/img/home/<USER>");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 271px 10px;

    .right-icon {
      display: none;
    }

    &.phonetic-btn--cancel {
      background-color: #c23640;
    }
  }
}

.right-icon {
  position: absolute;
  top: 50%;
  right: 0;
  z-index: 2;
  width: 34px;
  height: 34px;
  transform: translateY(-50%);
}
</style>
