<script lang="ts" setup>
import { Popup, Button } from 'vant';

import namespaceT from '@/helps/namespace-t';

interface EmitType {
  (e: 'on-confirm'): void;
}
const emit = defineEmits<EmitType>();


const show = defineModel<boolean>();
const t = namespaceT('home');

function onCancel() {
  show.value = false;
}

function onConfirm() {
  emit('on-confirm');
}
</script>


<template>
  <Popup
    v-model:show="show"
    close-on-popstate
    class="confirm-popup"
  >
    <div class="title">
      {{ t('hint.delAllHistoryRecord') }}
    </div>

    <div class="actions">
      <Button
        type="default"
        class="cancel-btn"
        @click="onCancel"
      >
        {{ t('action.cancel') }}
      </Button>
      <Button
        type="primary"
        class="confirm-btn"
        @click="onConfirm"
      >
        {{ t('action.confirm') }}
      </Button>
    </div>
  </Popup>
</template>


<style lang="less" scoped>
.confirm-popup {
  width: 80%;
  padding: 24px;
  border-radius: 12px;

  .title {
    color: #222;
    font-weight: 500;
    font-size: 18px;
    text-align: center;
  }

  .actions {
    display: flex;
    align-items: center;
    margin-top: 24px;

    :deep(.van-button) {
      flex: 1;
      height: 42px;
      line-height: 42px;
      border-radius: 6px;
    }

    .cancel-btn {
      margin-right: 12px;
      color: #444;
      border-radius: 1px solid #f5f5f5;
    }

    .confirm-btn {
      background-color: #1480ff;
      border-radius: 1px solid #1480ff;
    }
  }
}
</style>
