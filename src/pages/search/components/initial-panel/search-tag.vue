<script lang="ts" setup>
import { Tag } from 'vant';

withDefaults(defineProps<{
  model: string;
}>(), {});
</script>


<template>
  <Tag
    type="primary"
    round
    class="search-tag"
    v-bind="$attrs"
  >
    {{ model }}
  </Tag>
</template>


<style lang="less" scoped>
.search-tag {
  padding: 6px 16px;
  color: #444;
  font-weight: 400;
  font-size: 12px;
  background-color: fade(#fff, 95%);
  box-shadow: 0 1px 4px 0 fade(#000, 4), 0 2px 6px 0 fade(#000, 8);
}
</style>
