<script lang="ts" setup>
withDefaults(defineProps<{
  title: string;
  clearable?: boolean;
}>(), {
  clearable: false,
});

interface EmitType {
  (e: 'on-clear'): void;
}
const emit = defineEmits<EmitType>();

function onClickClear() {
  emit('on-clear');
}
</script>


<template>
  <div class="title-bar">
    <span class="title">{{ title }}</span>
    <div
      v-if="clearable"
      class="delete-wrapper"
      @click="onClickClear"
    >
      <img
        src="@/assets/img/home/<USER>"
        class="delete-icon"
      >
    </div>
  </div>
</template>


<style lang="less" scoped>
.title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title {
  color: #111;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
}

.delete-wrapper {
  display: flex;
  align-items: center;
  height: 20px;
}
</style>
