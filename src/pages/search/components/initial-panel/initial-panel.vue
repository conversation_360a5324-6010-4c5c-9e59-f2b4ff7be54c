<script setup lang="ts">
import { ref } from 'vue';

import PageLoading from '@/components/common/page-loading.vue';
import SearchRecordWrapper from './search-record-wrapper.vue';
import TitleBar from './title-bar.vue';
import SearchTag from './search-tag.vue';
import PopupClearAll from './popup-clear-all.vue';

import { AppStorageKey } from '@/config/app-storage';
import { removeAppStorageItem } from '@/helps/app-storage';
import namespaceT from '@/helps/namespace-t';
import { SearchRecordType } from '../../helps/type';

withDefaults(defineProps<{
  loading?: boolean;
}>(), {
  loading: false,
});

interface EmitType {
  (e: 'on-search', keyword: string): void;
}
const emit = defineEmits<EmitType>();

const model = defineModel<SearchRecordType>();
const t = namespaceT('home.title');
const showClearAllPopup = ref(false);

// 点击历史记录
function onSearch(item: string) {
  emit('on-search', item);
}

function onClearHistory() {
  showClearAllPopup.value = true;
}

async function onConfirmClearAll() {
  showClearAllPopup.value = false;
  model.value.history = [];
  removeAppStorageItem(AppStorageKey.WITH_AI_SEARCH_HISTORY);
}
</script>


<template>
  <PageLoading v-if="loading" />
  <!-- 搜索历史 -->
  <SearchRecordWrapper v-if="model.history?.length">
    <template #title>
      <TitleBar
        :title="t('searchHistory')"
        clearable
        @on-clear="onClearHistory"
      />
    </template>

    <SearchTag
      v-for="item in model.history"
      :key="`history-${item}`"
      :model="item"
      @click="onSearch(item)"
    />
  </SearchRecordWrapper>

  <!-- 热门搜索 -->
  <SearchRecordWrapper v-if="model.hot?.length">
    <template #title>
      <TitleBar
        :title="t('searchHot')"
      />
    </template>
    <SearchTag
      v-for="item in model.hot"
      :key="`hot-${item}`"
      :model="item"
      @click="onSearch(item)"
    />
  </SearchRecordWrapper>

  <PopupClearAll
    v-model="showClearAllPopup"
    @on-confirm="onConfirmClearAll"
  />
</template>
