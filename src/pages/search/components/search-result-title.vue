<script lang="ts" setup>
withDefaults(defineProps<{
  title: string;
  num: number;
}>(), {});
</script>


<template>
  <div class="tip">
    {{ title }}
    <span class="num">({{ num }})</span>
  </div>
</template>


<style lang="less" scoped>
@import '@/styles/variables.less';

.tip {
  margin-bottom: 8px;
  color: @black-1;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;

  .num {
    margin-left: 4px;
    color: @black-9;
    font-weight: 400;
  }
}
</style>
