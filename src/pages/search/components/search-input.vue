<script lang="ts" setup>
import { Search } from 'vant';
import { ref } from 'vue';

import namespaceT from '@/helps/namespace-t';

defineProps<{
  showMicrophoneIcon?: boolean;
}>();

interface EmitType {
  (e: 'on-search', keyword: string): void;
}
const emit = defineEmits<EmitType>();


const t = namespaceT('home.placeholder');
const isPhonetic = defineModel<boolean>('isPhonetic');
const model = defineModel<string>();
const searchInputRef = ref();


function onClickRightIcon() {
  if (!model.value) {
    isPhonetic.value = true;
    return;
  }
  emit('on-search', model.value);
}

function onSearch() {
  emit('on-search', model.value);
}

function focus() {
  searchInputRef.value.focus();
}

defineExpose({
  focus,
});
</script>


<template>
  <Search
    v-show="!isPhonetic"
    ref="searchInputRef"
    v-model="model"
    :left-icon="''"
    clearable
    class="search-input"
    :placeholder="t('search')"
    v-bind="$attrs"
    @click-right-icon="onClickRightIcon"
    @search="onSearch"
  >
    <template #right-icon>
      <img
        v-show="model?.length"
        src="@/assets/img/home/<USER>"
        alt="microphone"
        class="right-icon"
      >
      <img
        v-show="!model?.length && showMicrophoneIcon"
        src="@/assets/img/home/<USER>"
        alt="microphone"
        class="right-icon"
      >
    </template>
  </Search>
</template>


<style lang="less" scoped>
.search-input {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 16px 12px 20px;
  background-color: transparent;

  :deep(.van-search__content) {
    height: 56px;
    padding: 0 12px;
    background-color: #fff;
    border-radius: 60px;
    box-shadow: 0 -1px 3px 0 fade(#000, 6%), 0 6px 9px 0 fade(#000, 12%);
  }

  :deep(.van-search__field) {
    height: 100%;
    padding-right: 0;
  }

  :deep(.van-cell) {
    line-height: 32px;
  }

  :deep(.van-field__control) {
    color: #222;
    font-weight: 400;
    font-size: 15px;

    &::placeholder {
      color: #888;
      font-weight: 400;
      font-size: 15px;
    }
  }

  .right-icon {
    width: 34px;
    height: 34px;
    vertical-align: middle;
    transition: all 0.3s ease-in-out;
  }
}
</style>
