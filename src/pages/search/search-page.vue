<script lang="ts" setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue';
import _ from 'lodash';

import { WithAiSearchApi } from '@/api/search/ai-search';
import { AssociatedSearchResultApi } from '@/api/search/associated-result';

import TitleBar from '@/components/common/title-bar.vue';
import SearchInput from './components/search-input.vue';
import PhoneticBtn from './components/phonetic-btn.vue';
import InitialPanel from './components/initial-panel';
import AssociatedResult from './components/associated-result.vue';
import SearchResult from './components/search-result.vue';

import { AppStorageKey } from '@/config/app-storage';
import { getAppStorageItem, setAppStorageItem } from '@/helps/app-storage';
import { openToastError } from '@/helps/toast';
import type { ServiceVO } from '@/pages/index/teacher-index/helps/type';
import namespaceT from '@/helps/namespace-t';
import { createSearchRecordModel } from './helps/model';
import type { SearchModelType } from './helps/type';
import { MAX_HISTORY_LENGTH } from './helps/config';

const te = namespaceT('apiError.aiSearch');

const keyword = ref('');
const searchInputRef = ref();
const initialLoading = ref(false);
const isPhonetic = ref(false); // 是否是語音輸入
const showSearchResult = ref(false); // 搜索結果
const showAssociatedResult = ref(false); // 聯想結果
const associatedList = ref<string[]>([]);

const searchRecord = reactive(createSearchRecordModel());
const searchModel = reactive<SearchModelType>({
  loading: false,
  error: false,
  finished: false,
  data: {
    service: [],
    relate: [],
  },
});
let requestFlag = 0;


async function fetchSearchAiApplication() {
  if (searchModel.loading) {
    return;
  }

  searchModel.loading = true;
  searchModel.error = false;
  searchModel.finished = false;

  requestFlag += 1;
  const currentFlag = requestFlag;

  try {
    const api = new WithAiSearchApi<ServiceVO[]>();
    api.data = {
      content: keyword.value,
    };
    const res = await api.send();

    if (currentFlag >= requestFlag) {
      searchModel.data.service = res;
      searchModel.finished = true;
    }
  } catch (error) {
    openToastError({ message: te('search') });
    searchModel.error = true;
    throw error;
  } finally {
    searchModel.loading = false;
  }
}

function storageHistorySearchRecord(query: string) {
  const idx = searchRecord.history.indexOf(query);
  if (idx > -1) {
    searchRecord.history.splice(idx, 1);
  }
  searchRecord.history.unshift(query);
  if (searchRecord.history?.length > MAX_HISTORY_LENGTH) {
    searchRecord.history.splice(MAX_HISTORY_LENGTH);
  }
  setAppStorageItem(AppStorageKey.WITH_AI_SEARCH_HISTORY, JSON.stringify(searchRecord.history));
}

function onSearch(query: string) {
  if (!query) {
    return;
  }

  keyword.value = query;
  showSearchResult.value = true;
  showAssociatedResult.value = false;
  storageHistorySearchRecord(query);
  fetchSearchAiApplication();
}

function onLoadSearchHistoryRecord() {
  const storageHistory = getAppStorageItem(AppStorageKey.WITH_AI_SEARCH_HISTORY);
  searchRecord.history = storageHistory ? JSON.parse(storageHistory) : [];
}

async function onSearchAssociatedResult(val) {
  showAssociatedResult.value = false;
  const api = new AssociatedSearchResultApi();
  api.params = {
    content: val,
  };
  const res = await api.send();
  associatedList.value = res;
  showAssociatedResult.value = false;
  await nextTick();
  showAssociatedResult.value = true;
}

const debouncedSearchAssociatedResult = _.debounce((val) => {
  onSearchAssociatedResult(val);
}, 1000);

function onKeywordInput(val) {
  // TODO:聯想搜索結果
  return;
  // eslint-disable-next-line no-unreachable
  debouncedSearchAssociatedResult(val);
}

onMounted(async () => {
  native.navigator.setNavigationBarHidden(true, false);

  onLoadSearchHistoryRecord();
  await nextTick();
  searchInputRef.value?.focus();
});

watch(() => isPhonetic.value, async (val) => {
  if (!val) {
    await nextTick();
    searchInputRef.value.focus();
  }
}, { immediate: true });

</script>


<template>
  <div class="search-page">
    <TitleBar />

    <div class="search-page-body">
      <!-- TODO:聯想結果 -->
      <AssociatedResult
        v-if="showAssociatedResult"
        :list="associatedList"
        :keyword="keyword"
        @on-search="onSearch"
      />
      <!-- 搜索結果 -->
      <SearchResult
        v-else-if="showSearchResult"
        :data="searchModel.data"
        :loading="searchModel.loading"
        :error="searchModel.error"
        :finished="searchModel.finished"
      />
      <!--搜索歷史 & 熱門搜索 -->
      <InitialPanel
        v-else
        v-model="searchRecord"
        :loading="initialLoading"
        @on-search="onSearch"
      />

      <!-- 搜索框 -->
      <SearchInput
        ref="searchInputRef"
        v-model="keyword"
        v-model:is-phonetic="isPhonetic"
        :readonly="searchModel.loading"
        @on-search="onSearch"
        @update:model-value="onKeywordInput"
      />

      <!-- 语音按钮 -->
      <PhoneticBtn
        v-model="keyword"
        v-model:is-phonetic="isPhonetic"
        @search="onSearch"
      />
    </div>
  </div>
</template>


<style lang="less" scoped>
@import "@/styles/variables.less";

.search-page {
  box-sizing: border-box;
  height: 100%;
  background-color: @page-bg;
}

.search-page-body {
  box-sizing: border-box;
  height: calc(100% - 50px);
  padding: 16px 20px 100px;
}
</style>
