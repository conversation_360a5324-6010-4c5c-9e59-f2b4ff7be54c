import { format, parse } from 'date-fns';
import { ApiDate } from '@/config/api';
import namespaceT from '@/helps/namespace-t';
import { textByLocale } from '@/helps/locale';
import { CampusActivityListApi } from '@/api/home/<USER>';


const td = namespaceT('dateFormat');

function coverTime(startTime, endTime) {
  const start = parse(startTime, ApiDate.FULL_DATE_TIME_WITH_TIME_ZONE, new Date());
  const end = parse(endTime, ApiDate.FULL_DATE_TIME_WITH_TIME_ZONE, new Date());

  return `${format(start, td('date'))} - ${format(end, td('date'))}`;
}

function getVOData(data) {
  const list = data ?? [];

  return list.map((item) => {
    return {
      id: item.id,
      title: textByLocale(item.title, item.enTitle, true),
      time: coverTime(item.startTime, item.endTime),
      // TODO: 待后端增加数据
      photo: item.photo,
      url: item.url,
      address: textByLocale(item.address, item.enAddress, true),
    };
  });
}

export class CampusActivityListService {
  async fetch() {
    const api = new CampusActivityListApi();
    const { data } = await api.send();

    return getVOData(data);
  }
}
