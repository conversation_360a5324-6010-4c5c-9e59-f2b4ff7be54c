import { startOfDay, endOfDay, format, isSameDay, parse, isValid } from 'date-fns';
import type { MessageInfo } from '@/pages/index/teacher-index/helps/type';
import namespaceT from '@/helps/namespace-t';
import { LatestScheduleMessageApi } from '@/api/home/<USER>';


const t = namespaceT('home');
const td = namespaceT('dateFormat');

// 日程事件详细数据结构
interface ScheduleEventDetail {
  /** 事件ID */
  id: number;
  /** 事件标题 */
  title: string;
  /** 开始日期 (格式: yyyy-MM-ddTHH:mm:ss) */
  eventStartDate: string;
  /** 开始时间 (格式: HH:mm) */
  eventStartTime: string;
  /** 结束日期 (格式: yyyy-MM-ddTHH:mm:ss) */
  eventEndDate: string;
  /** 结束时间 (格式: HH:mm) */
  eventEndTime: string;
  /** 开始日期其他格式 */
  eventStartDateOther?: string;
  /** 结束日期其他格式 */
  eventEndDateOther?: string;
  /** 地址 */
  address: string;
  /** 计划ID */
  planId: string;
  /** 关联类型 */
  relateType: string;
}

/**
   * 格式化时间范围显示
   * @param startDate 开始日期 (格式: yyyy-MM-ddTHH:mm:ss)
   * @param startTime 开始时间 (格式: HH:mm)
   * @param endDate 结束日期 (格式: yyyy-MM-ddTHH:mm:ss)
   * @param endTime 结束时间 (格式: HH:mm)
   * @param startDateOther 开始日期其他格式，有值时将startTime设为00:00
   * @param endDateOther 结束日期其他格式，有值时将endTime设为23:59
   * @returns 格式化后的时间字符串
   */
function coverTime({
  startDate,
  startTime,
  endDate,
  endTime,
  startDateOther,
  endDateOther,
}): string {
  // 根据 startDateOther 和 endDateOther 调整时间
  let adjustedStartTime = startTime;
  let adjustedEndTime = endTime;

  if (startDateOther && startDateOther.trim() !== '') {
    adjustedStartTime = '00:00';
  }

  if (endDateOther && endDateOther.trim() !== '') {
    adjustedEndTime = '23:59';
  }

  // 从ISO格式日期中提取日期部分，然后与时间组合
  const startDateOnly = startDate.split('T')[0]; // 提取 yyyy-MM-dd 部分
  const endDateOnly = endDate.split('T')[0]; // 提取 yyyy-MM-dd 部分

  // 解析开始和结束的完整日期时间
  const startDateTime = parse(`${startDateOnly} ${adjustedStartTime}`, td('dateTime'), new Date());
  const endDateTime = parse(`${endDateOnly} ${adjustedEndTime}`, td('dateTime'), new Date());

  // 验证日期是否有效
  if (!isValid(startDateTime) || !isValid(endDateTime)) {
    return `${adjustedStartTime} - ${adjustedEndTime}`;
  }

  const today = new Date();
  const isStartToday = isSameDay(startDateTime, today);
  const isEndToday = isSameDay(endDateTime, today);

  // 格式化时间的辅助函数
  const formatTimeWithPeriod = (dateTime: Date): string => {
    const hour = dateTime.getHours();
    const timeStr = format(dateTime, 'HH:mm');
    return hour < 12 ? `${t('date.am')} ${timeStr}` : `${t('date.pm')} ${timeStr}`;
  };

  // 情况1: 两个日期都不是当天
  if (!isStartToday && !isEndToday) {
    const startStr = format(startDateTime, td('dateTime'));
    const endStr = format(endDateTime, td('dateTime'));
    return `${startStr} - ${endStr}`;
  }

  // 情况2: 两个日期都是当天
  if (isStartToday && isEndToday) {
    const startStr = formatTimeWithPeriod(startDateTime);
    const endTimeStr = format(endDateTime, 'HH:mm');
    return `${startStr} - ${endTimeStr}`;
  }

  // 情况3: 只有一个日期是当天
  if (isStartToday && !isEndToday) {
    // 开始日期是当天，结束日期不是当天
    const startStr = formatTimeWithPeriod(startDateTime);
    const endStr = format(endDateTime, td('dateTime'));
    return `${startStr} - ${endStr}`;
  }
  // 开始日期不是当天，结束日期是当天
  const startStr = format(startDateTime, td('dateTime'));
  const endStr = formatTimeWithPeriod(endDateTime);
  return `${startStr} - ${endStr}`;
}

function findTodayLastScheduleEvent(data) {
  if (!data) {
    return null;
  }

  const sameDay = data.find((item) => isSameDay(new Date(item.date), new Date()));

  if (sameDay && sameDay.events.length > 0) {
    return sameDay.events[sameDay.events.length - 1];
  }

  return null;
}

function covertToMessageInfo(data: ScheduleEventDetail): MessageInfo {
  return {
    id: data.id,
    title: data.title,
    address: data.address,
    planId: data.planId,
    relateType: data.relateType,
    time: coverTime({
      startDate: data.eventStartDate,
      startTime: data.eventStartTime,
      endDate: data.eventEndDate,
      endTime: data.eventEndTime,
      startDateOther: data.eventStartDateOther,
      endDateOther: data.eventEndDateOther,
    }),
  };
}

export class LatestScheduleMessageService {
  /**
   * 获取当天的最后一条日程消息
   * @returns Promise<MessageInfo> 日程消息信息
   */
  async fetch(): Promise<MessageInfo> {
    const api = new LatestScheduleMessageApi();
    const today = new Date();

    // 设置查询参数：当天的开始和结束时间
    api.params = {
      startTime: format(startOfDay(today), td('date')),
      endTime: format(endOfDay(today), td('date')),
    };

    const res = await api.send();
    const lastSchedule = findTodayLastScheduleEvent(res);

    if (lastSchedule) {
      return covertToMessageInfo(lastSchedule);
    }

    return null;
  }
}
