import { textByLocale } from '@/helps/locale';
import { NewsInformationListApi } from '@/api/home/<USER>';


function getVOData(data) {
  const list = data ?? [];

  return list.map((item) => {
    return {
      id: item.id,
      title: textByLocale(item.titleTrad, item.titleEng, true),
      time: '',
      // TODO: 待后端增加数据
      photo: '',
      url: item.url,
    };
  });
}

export class NewsInformationListService {
  async fetch() {
    const api = new NewsInformationListApi();
    const { data } = await api.send();

    return getVOData(data);
  }
}
