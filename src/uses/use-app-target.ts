import { Ref } from 'vue';

import { AppTarget } from '@/consts/app-target';
import { ServiceCode } from '@/consts/service-code';
import { ServiceVO } from '@/pages/index/teacher-index/helps/type';

type AppTargetJumpFn = (params?: ServiceVO, eventName?: string) => void;

interface AppTargetResult {
  getAppJumpFn: (target: AppTarget) => AppTargetJumpFn;
  getCreateOptionJumpFn: (params: ServiceVO) => () => void;
}

export function useAppTarget(application?: Ref<ServiceVO>): AppTargetResult {
  const appTargetMapper = new Map<AppTarget, AppTargetJumpFn>([
    [AppTarget.INTERNAL_APP, (params: ServiceVO, eventName = 'serviceclicked') => {
      return native.navigator.v2.launchService({
        serviceCode: params?.code || application.value.code,
        eventName,
      });
    }],
    [AppTarget.EXTERNAL_APP, (params) => {
      native.navigator.launchWebView(params?.url || application.value.url);
    }],
    [AppTarget.EXTERNAL_BROWSER, (params) => {
      // 《蓝牙设定》应用特殊处理逻辑
      // 该应用为原生应用，只存在Android端
      const serviceCode = params?.code || application.value.code;
      if (serviceCode === ServiceCode.S_WM_PUNCH_APP) {
        native.ibeacon.launchBeaconSettingService();
        return;
      }

      native.navigator.openURL(params?.url || application.value.url);
    }],
  ]);

  function getAppJumpFn(target: AppTarget) {
    return appTargetMapper.get(target);
  }


  function getCreateOptionJumpFn(option: ServiceVO) {
    const jumpFn = appTargetMapper.get(option.target);
    return () => jumpFn(option, 'quicklinkclicked');
  }


  return {
    getAppJumpFn,
    getCreateOptionJumpFn,
  };
}
