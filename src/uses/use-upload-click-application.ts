import { HomeUploadServiceHitApi } from '@/api/home/<USER>';

interface UploadClickApplicationResult {
  uploadClickApplication: (code: string) => Promise<void>;
}

export function useUploadClickApplication(): UploadClickApplicationResult {
  async function fetch(code: string) {
    try {
      const api = new HomeUploadServiceHitApi();
      api.params = {
        code,
      };
      await api.send();
    } catch {
      // do nothing
    }
  }

  async function uploadClickApplication(code: string) {
    await fetch(code);
  }


  return {
    uploadClickApplication,
  };
}
