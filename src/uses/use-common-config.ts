import { computed, ComputedRef, Ref, ref } from 'vue';

import { CommonConfigApi } from '@/api/common-config';

import { AppStorageKey } from '@/config/app-storage';
import { OperatingCode } from '@/consts/operating-code';
import { hasOperating } from '@/helps/has-operating-code';
import { setAppStorageItem } from '@/helps/app-storage';
import { isNothing } from '@/utils/is-nothing';

import type { CommonConfig } from '@/types/common-config';


export interface CommonConfigResult {
  model: Ref<CommonConfig>;
  isShowScan: ComputedRef<boolean>;
  isShowDebug: ComputedRef<boolean>;

  loadCommonConfig: () => Promise<void>;
}


export function useCommonConfig() {
  const model = ref<CommonConfig>({
    operatingList: [],
    paramList: [],
  });

  const isShowScan = computed(() => {
    return hasOperating(model.value.operatingList, OperatingCode.SCAN_ACCESS);
  });

  const isShowDebug = computed(() => {
    return hasOperating(model.value.operatingList, OperatingCode.DEBUG);
  });

  async function loadCommonConfig() {
    const api = new CommonConfigApi<CommonConfig>();
    const res = await api.send();

    if (!isNothing(res)) {
      model.value = res;
      setAppStorageItem(AppStorageKey.COMMONLY_USED_FUNCTIONS, res);
    }
  }

  return {
    model,
    isShowScan,
    isShowDebug,

    loadCommonConfig,
  };
}
