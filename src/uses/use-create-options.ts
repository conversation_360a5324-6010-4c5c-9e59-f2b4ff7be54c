import type { PopoverAction } from 'vant';
import { ProjectCategoryList, ServiceVO } from '@/pages/index/teacher-index/helps/type';
import namespaceT from '@/helps/namespace-t';

import IconDts from '@/assets/img/new/dts.png';
import IconLeave from '@/assets/img/new/leave.png';
import IconOvertime from '@/assets/img/new/overtime.png';
import IconRepair from '@/assets/img/new/repair.png';
import IconScheduleNew from '@/assets/img/new/schedule-new.png';
import { computed, ComputedRef, Ref } from 'vue';


type OptionResult = () => PopoverAction;
export type CreateOption = PopoverAction & ServiceVO;

interface CreateOptionsResult {
  createOptions: ComputedRef<CreateOption[]>
}

export function useCreateOptions(applicationList: Ref<ProjectCategoryList[]>): CreateOptionsResult {
  const t = namespaceT('home.createOptions');

  const newOptionsMap = new Map<string, OptionResult>([
    /** 公文  */
    ['S-WM-DTS', () => {
      return {
        text: t('S-WM-DTS'),
        icon: IconDts,
      };
    }],
    /** 加班 */
    ['S-WM-OVERTIME', () => {
      return {
        text: t('S-WM-OVERTIME'),
        icon: IconOvertime,
      };
    }],
    /** 请假 */
    ['S-WM-LEAVE', () => {
      return {
        text: t('S-WM-LEAVE'),
        icon: IconLeave,
      };
    }],
    /** 日程 */
    ['S-WM-SCHEDULE-NEW-APP', () => {
      return {
        text: t('S-WM-SCHEDULE-NEW-APP'),
        icon: IconScheduleNew,
      };
    }],
    /** 报修 */
    ['S-WM-REPAIR', () => {
      return {
        text: t('S-WM-REPAIR'),
        icon: IconRepair,
      };
    }],
  ]);

  const allApplication = computed(() => {
    return applicationList.value.map((item) => item.serviceList.flat()).flat();
  });

  const allApplicationCode = computed(() => allApplication.value.map((item) => item.code));

  const createOptions = computed<CreateOption[]>(() => {
    const optionKeys = Array.from(newOptionsMap.keys());
    const exitsOptionKey = optionKeys.filter((key) => allApplicationCode.value.includes(key));

    return exitsOptionKey.map((key) => {
      const action = newOptionsMap.get(key)();
      const service = allApplication.value.find((item) => item.code === key);

      // 注：日程需要特殊處理，添加add路徑
      if (key === 'S-WM-SCHEDULE-NEW-APP') {
        return { ...service, ...action, url: `${service.url}/add` };
      }

      return { ...service, ...action };
    });
  });


  return {
    createOptions,
  };
}
