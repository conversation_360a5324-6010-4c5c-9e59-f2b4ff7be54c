interface LongPressParams {
  delay?: number;
  isNeedClick?: boolean;

  onLongPressCallback: () => void;
  onClick?: () => void;
}

interface LongPressResult {
  onTouchStart: () => void;
  onTouchMove: () => void;
  onTouchEnd: () => void;
}


export function useLongPress(params: LongPressParams): LongPressResult {
  const LONG_PRESS_DELAY = params.delay || 500;
  const isHasClickFn = params.isNeedClick || false;
  let longPressTimeout = null;
  let isScrolling = false;
  let touchStartTime = 0;

  function onTouchStart() {
    if (longPressTimeout) {
      clearTimeout(longPressTimeout);
    }

    isScrolling = false;
    touchStartTime = new Date().getTime();

    longPressTimeout = setTimeout(() => {
      if (!isScrolling) {
        params.onLongPressCallback();
      }
    }, LONG_PRESS_DELAY);
  }

  function onTouchMove() {
    isScrolling = true;
  }

  function onTouchEnd() {
    clearTimeout(longPressTimeout);
    const touchEndTime = new Date().getTime();
    const duration = touchEndTime - touchStartTime;
    const isLongPress = duration >= LONG_PRESS_DELAY;

    if (!isLongPress && !isScrolling && isHasClickFn) {
      params.onClick();
    }
  }

  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd,
  };
}
