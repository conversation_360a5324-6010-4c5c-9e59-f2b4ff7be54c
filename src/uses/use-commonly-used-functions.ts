import { ref, Ref, watch } from 'vue';

import { OperatingCode } from '@/consts/operating-code';
import { hasOperating } from '@/helps/has-operating-code';
import { getOperatingCodeI18nText } from '@/helps/i18n/operating-code';
import { isTeacher } from '@/helps/is-teacher';

import clockInStudent from '@/assets/img/commonly-used-functions/clock-in-student.png';
import clockInTeacher from '@/assets/img/commonly-used-functions/clock-in-teacher.png';
import entranceGuardStudent from '@/assets/img/commonly-used-functions/entrance-guard-student.png';
import entranceGuardTeacher from '@/assets/img/commonly-used-functions/entrance-guard-teacher.png';
import rechargeStudent from '@/assets/img/commonly-used-functions/recharge-student.png';
import rechargeTeacher from '@/assets/img/commonly-used-functions/recharge-teacher.png';
import walletStudent from '@/assets/img/commonly-used-functions/wallet-student.png';
import walletTeacher from '@/assets/img/commonly-used-functions/wallet-teacher.png';

import type { Operating } from '@/types/common-config';


export interface CommonlyUsedFunction {
  code: OperatingCode;
  name: string;
  isShow: boolean;
  icon: string;
  run: () => void;
}

type MapResult = () => CommonlyUsedFunction;

interface CommonlyUsedFunctionsResult {
  commonlyUsedFunctionList: Ref<CommonlyUsedFunction[]>;
}

type IconResult = () => string;


const iconMap = new Map<OperatingCode, IconResult>([
  [OperatingCode.CLOCK_IN_ACCESS, () => {
    return isTeacher() ? clockInTeacher : clockInStudent;
  }],
  [OperatingCode.CLOCK_IN_DORMITORY, () => {
    return isTeacher() ? clockInTeacher : clockInStudent;
  }],
  [OperatingCode.ENTRANCE_GUARD_ACCESS, () => {
    return isTeacher() ? entranceGuardTeacher : entranceGuardStudent;
  }],
  [OperatingCode.RECHARGE_ACCESS, () => {
    return isTeacher() ? rechargeTeacher : rechargeStudent;
  }],
  [OperatingCode.WALLET_ACCESS, () => {
    return isTeacher() ? walletTeacher : walletStudent;
  }],
]);


export function useCommonlyUsedFunctions(operatingList: Ref<Operating[]>): CommonlyUsedFunctionsResult {
  const commonlyUsedFunctionMap = new Map<OperatingCode, MapResult>([
    /** 门禁 */
    [OperatingCode.ENTRANCE_GUARD_ACCESS, () => {
      return {
        code: OperatingCode.ENTRANCE_GUARD_ACCESS,
        name: getOperatingCodeI18nText(OperatingCode.ENTRANCE_GUARD_ACCESS),
        isShow: hasOperating(operatingList.value, OperatingCode.ENTRANCE_GUARD_ACCESS),
        icon: iconMap.get(OperatingCode.ENTRANCE_GUARD_ACCESS)(),
        run: () => native.navigator.fastService('S-WM-ACCESS-CONTROL-APP'),
      };
    }],

    /** 打卡 */
    [OperatingCode.CLOCK_IN_ACCESS, () => {
      return {
        code: OperatingCode.CLOCK_IN_ACCESS,
        name: getOperatingCodeI18nText(OperatingCode.CLOCK_IN_ACCESS),
        isShow: hasOperating(operatingList.value, OperatingCode.CLOCK_IN_ACCESS),
        icon: iconMap.get(OperatingCode.CLOCK_IN_ACCESS)(),
        run: () => native.navigator.fastService('S-WM-ATTENDANCE-APP'),
      };
    }],

    /** 宿舍打卡 */
    [OperatingCode.CLOCK_IN_DORMITORY, () => {
      return {
        code: OperatingCode.CLOCK_IN_DORMITORY,
        name: getOperatingCodeI18nText(OperatingCode.CLOCK_IN_DORMITORY),
        isShow: hasOperating(operatingList.value, OperatingCode.CLOCK_IN_DORMITORY),
        icon: iconMap.get(OperatingCode.CLOCK_IN_DORMITORY)(),
        run: () => native.navigator.fastService('S-WM-HOSTEL-APP'),
      };
    }],

    /** 付款码 */
    [OperatingCode.WALLET_ACCESS, () => {
      return {
        code: OperatingCode.WALLET_ACCESS,
        name: getOperatingCodeI18nText(OperatingCode.WALLET_ACCESS),
        isShow: hasOperating(operatingList.value, OperatingCode.WALLET_ACCESS),
        icon: iconMap.get(OperatingCode.WALLET_ACCESS)(),
        run: () => native.payment.showPaymentCode(),
      };
    }],

    /** 充值 */
    [OperatingCode.RECHARGE_ACCESS, () => {
      return {
        code: OperatingCode.RECHARGE_ACCESS,
        name: getOperatingCodeI18nText(OperatingCode.RECHARGE_ACCESS),
        isShow: hasOperating(operatingList.value, OperatingCode.RECHARGE_ACCESS),
        icon: iconMap.get(OperatingCode.RECHARGE_ACCESS)(),
        run: () => native.navigator.v2.launchService?.({
          serviceCode: 'S-WM-PAY-WALLET',
          eventName: 'rechargeindex',
        }),
      };
    }],
  ]);

  function getFunctionList() {
    return Array.from(commonlyUsedFunctionMap.values())
      .filter((item) => {
        const { isShow, code } = item();

        if (code === OperatingCode.CLOCK_IN_ACCESS) {
          return isTeacher() && isShow;
        }

        if (code === OperatingCode.CLOCK_IN_DORMITORY) {
          return !isTeacher() && isShow;
        }

        return isShow;
      })
      .map((fn) => fn());
  }

  const commonlyUsedFunctionList = ref(getFunctionList());
  watch(() => operatingList, () => {
    commonlyUsedFunctionList.value = getFunctionList();
  }, { deep: true });


  return {
    commonlyUsedFunctionList,
  };
}
