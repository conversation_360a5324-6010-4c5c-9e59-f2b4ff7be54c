import { Component, createApp } from 'vue';
import type { Composer } from 'vue-i18n';
import { createPinia } from 'pinia';

import { InjectPrototypePlugin } from '@/plugins/inject-prototype';
import { VantPlugin } from '@/plugins/vant';

import { NativeEvent } from '@/config/native-event';
import { i18n } from '@/i18n';
import eventBus from '@/utils/event-bus';
import { createErudaIfNeed } from '@/utils/create-eruda-if-need';
import { isInIos } from '@/utils/browser';

// 基础样式
import '@/styles/reset.less';
import '@/styles/app.less';
import '@/styles/utils.less';
import 'vant/lib/index.css';


function useCreateApp(component: Component) {
  const app = createApp(component);

  eventBus.on('invaildAccessTokenError', () => {
    native.events.dispatchEvent('accesstokenexpries');
  });

  (i18n.global as unknown as Composer).locale.value = window.native.i18n.language;

  app.use(i18n);
  app.use(InjectPrototypePlugin); // 全局方法
  app.use(VantPlugin, { i18n });
  app.use(createPinia());
  app.mount('#app');
}

export function createAppWhenNativeReady(component: Component) {
  /**
   * 创建调试工具
   * 在env文件中配置
  */
  createErudaIfNeed();

  window.addEventListener(NativeEvent.NATIVE_READY, () => {
    // iOS下设置界面不能滚动，Android不可设置（设置导致页面无法正常滚动）
    if (isInIos()) {
      window.native.navigator.setScrollEnabled(false);
    }

    useCreateApp(component);
  });

  window.clientready = true;
}
