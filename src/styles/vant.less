@import "./variables.less";

@white: #fff;
@red: #ff5c5c;
@blue: #4476d5;
@green: #4caf4b;
@orange: #eb6c00;

@button-default-color: #222;
@button-primary-background-color: #4476d5;
@button-primary-border-color: #4476d5;
@checkbox-checked-icon-color: #4476d5;
@radio-checked-icon-color: #4476d5;
@search-padding: 6px 16px;
@search-content-background-color: #f4f4f4;
@search-input-height: 32px;
@search-label-color: #888;
@search-label-font-size: 15px;
@tab-text-color: #555;
@tab-active-text-color: #4476d5;
@tab-font-size: 17px;
@tabs-default-color: #4476d5;
@tabs-line-height: 56px;
@tabs-bottom-bar-height: 1px;
@cell-font-size: 16px;
// 自定义
@primary: #4476d5;
@background-color: #f4f4f4;
@highlight-background-color: #e8edf7;


.wm {
  &.van-form {
    .van-field {
      position: relative;
      padding: 18px 20px;
      color: #222;
      font-weight: 400;
      font-size: 16px;

      &::after {
        right: 0;
        left: 0;
        border-bottom-color: #ededed;
      }

      .van-field__value {
        .van-field__control.van-field__control--custom {
          .placeholder {
            color: #888;
          }
        }

        input.van-field__control::placeholder {
          color: #888;
        }

        textarea.van-field__control::placeholder {
          color: #888;
        }

        .van-field__error-message {
          color: #ff5c5c;
          font-size: 14px;
        }

        .van-field__word-limit {
          color: #888;
        }
      }

      &.van-field--error {
        .van-field__control {
          color: #ff5c5c;
        }
      
        .van-field__control::placeholder {
          color: #ff5c5c;
        }
      }
    }
  }

  &.van-cell-group {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    &.van-hairline--top-bottom {
      &::after {
        border-bottom-color: #ededed;
      }
    }

    .van-cell {
      padding: 18px 20px;
      font-weight: 400;
      font-size: 16px;

      &::after {
        right: 0;
        left: 0;
        border-bottom-color: #ededed;
      }
  
      .van-cell__title {
        color: #222;

        .van-cell__label {
          margin-top: 10px;
          color: #555;
          font-weight: 400;
          font-size: 16px;
        }
      }

      .van-cell__value {
        color: #555;
      }
    }
  }

  &.van-button {
    font-weight: 400;

    &.van-button--primary {
      background-color: #365aa4;
      border-color: #365aa4;
    }

    &.van-button--default {
      color: #365aa4;
      border-color: #365aa4;
      background-color: transparent;
    }

    &.van-button--large {
      font-size: 17px;
      border-radius: 3px;
    }

    &.van-button--normal {
      font-size: 15px;
      border-radius: 2px;
    }
  }

  &.van-search {
    padding: 6px 16px;

    &.van-search--show-action {
      padding-right: 0;
    }

    .van-search__content {
      padding-left: 10px;
      background-color: #f4f4f4;

      &.van-search__content--round {
        border-radius: 5px;
      }

      .van-field__left-icon {
        .van-icon-search {
          width: 16px;
          height: 24px;
    
          &::before {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("~@/assets/img/icon-search.png") no-repeat center center / 100% auto;
            content: " ";
          }
        }
      }

      .van-field__body {
        font-weight: 400;
        font-size: 15px;

        .van-field__control::placeholder {
          color: #888;
        }
      }

      .van-search__action {
        padding: 0 20px;
        color: #222;
        font-weight: 400;
        font-size: 15px;
      }
    }
  }

  &.van-overlay {
    background-color: rgba(0, 0, 0, 0.45);
  }

  &.van-picker {
    & > .van-picker__toolbar {
      & > .van-picker__confirm {
        color: #365aa4;
      }
    }
  }

  &.van-collapse {
    .van-collapse-item {
      .van-collapse-item__title {
        // collapseItem展开的动画有对应的事件，并且在最后会将其高度设置为auto
        // 但由于业务需求，动画被去除掉了，因此高度直接设置为auto，无需动态变化
        // 不然会出现内容高度展示不全问题。
        &.van-collapse-item__title--expanded ~ .van-collapse-item__wrapper {
          height: auto !important;
        }
      }
    }
  }

  &.van-radio {
    & > .van-radio__label {
      color: @black-2;
      font-size: 15px;
      line-height: 18px;
    }

    & > .van-radio__icon {
      width: 16px;
      height: 16px;

      & > .van-radio__icon--dot__icon {
        width: 8px;
        height: 8px;
      }
    }
  }

  & .van-calendar {
    .van-calendar__day--selected .van-calendar__selected-day {
      background-color: #365aa4;
    }
    
    .van-calendar__footer {
      .van-button {
        border-color: #365aa4;
        background-color: #365aa4;
      }
    }
  }
}
