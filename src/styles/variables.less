// 占位用，不起作用，以主题为准
// 规则：
// - 如果后面的样式的值是一样的，命名则只写前两个/一个字， 如： @bg-f8: #f8f8f8; @black-9: #999;
// - 如果后面的样式的值是不一样的，命名则写全，如： @font-12: 12px;

@white: #ffffff;
@primary: #365aa4;
@primary-blue: #4476D5;
@primary-red: #f53530;
@primary-black: #515a6e;
@primary-green: #4caf4b;
@primary-gray: #888;

@border-d9: #d9d9d9;
@border-c0: #c0c0c0;
@border-ed: #ededed;
@border-e4: #e4e4e4;
@border-f2: #f2f2f2;
@border-da: #dadada;
@border-0008: rgba(0, 0, 0, 0.08);

@black-1: #111;
@black-2: #222;
@black-3: #333;
@black-4: #444;
@black-5: #555;
@black-6: #666;
@black-7: #777;
@black-8: #888;
@black-9: #999;

@font-12: 12px;
@font-14: 14px;
@font-16: 16px;
@font-18: 18px;
@font-20: 20px;

@bg-f4: #f4f4f4;
@bg-f8: #f8f8f8;
@bg-c7: #c7c7c7;

@page-bg: #f1f2f6;