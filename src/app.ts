if (typeof window.App === 'undefined') {
  throw new Error('function window.App not found.');
}

const NativeEvent = Object.freeze({
  PUSH_CLICKED: 'pushclicked', // 远程推送消息点击
  MESSAGE_CLICKED: 'messageclicked', // 消息列表点击
  SERVICE_CLICKED: 'serviceclicked', // 服务点击
});

// function handlePushClickedCallback() {
//   // TODO: 处理推送消息点击事件
// }

// function handleMessageClickedCallback() {
//   // TODO: 处理消息列表点击事件
// }


window.App({
  homePage() {
    // 首页路径
    return 'index.html';
  },

  // 当用户开启业务应用时触发
  onLaunch() {
    return null;
  },

  // 当接收到通知时触发
  onNotify(event) {
    switch (event.type) {
      case NativeEvent.PUSH_CLICKED:
        // TODO
        // handlePushClickedCallback(event.param);
        break;
      case NativeEvent.MESSAGE_CLICKED:
        // TODO
        // handleMessageClickedCallback(event.param);
        break;
      default:
        break;
    }
  },
});
