import { App } from 'vue';
import { Locale } from 'vant';
import { I18n } from 'vue-i18n';
import type { Composer } from 'vue-i18n';
import enUS from 'vant/lib/locale/lang/en-US';
import zhHK from 'vant/lib/locale/lang/zh-HK';

export const VantPlugin = {
  install(app: App, options: { i18n: I18n }) {
    if (!options?.i18n) {
      throw new Error('Please init i18n option');
    }

    const locale = (options.i18n.global as unknown as Composer).locale.value;

    switch (locale) {
      case 'en':
        Locale.use('en-US', enUS);
        break;
      default:
        Locale.use('zh-Hant', zhHK);
        break;
    }
  },
};
