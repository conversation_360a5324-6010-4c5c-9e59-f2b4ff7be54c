import { App } from 'vue';
import { dateFormatSTZ, dateFormat } from '@/helps/date';
import { valueByLocale } from '@/helps/locale';
import namespaceT from '@/helps/namespace-t';


export const InjectPrototypePlugin = {
  install(app: App) {
    const t = namespaceT();

    Object.assign(app.config.globalProperties, {
      $dateFormatSTZ: dateFormatSTZ,
      $dateFormat: dateFormat,
      $judgeLanguage: valueByLocale,
      $t: t,
    });
  },
};
